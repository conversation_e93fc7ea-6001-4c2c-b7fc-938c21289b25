/** @type {import('next').NextConfig} */

// Load environment variables
require("dotenv").config({ path: ".env.local" });

// Import the base config
const baseConfig = require("./next.config.js");

// Create a development-specific config
const devConfig = {
  ...baseConfig,

  // Override the allowedDevOrigins to include all origins
  experimental: {
    ...baseConfig.experimental,
  },

  // Set allowedDevOrigins to true to allow all origins in development
  allowedDevOrigins: true,

  // Ensure CORS headers are set correctly
  async headers() {
    return [
      {
        source: "/(.*)",
        headers: [
          {
            key: "Access-Control-Allow-Origin",
            value: "*",
          },
          {
            key: "Access-Control-Allow-Methods",
            value: "GET, POST, PUT, DELETE, OPTIONS",
          },
          {
            key: "Access-Control-Allow-Headers",
            value: "X-Requested-With, Content-Type, Authorization",
          },
          {
            key: "Access-Control-Allow-Credentials",
            value: "true",
          },
        ],
      },
    ];
  },
};

module.exports = devConfig;
