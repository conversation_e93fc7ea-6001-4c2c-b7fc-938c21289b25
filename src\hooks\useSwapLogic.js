// Custom hook for swap business logic
import { useState, useRef, useCallback, useContext } from 'react';
import { BlockchainContext } from '../context/BlockchainContext';
import { getUniswapQuoteV3, getAmountOutV2 } from '../utils/swapUtils';

export const useSwapLogic = (sellToken, buyToken, ALL_TOKENS, chain_id, authToken) => {
  const {
    account,
    savedInputAmount,
    updateData,
    signer,
    provider,
    savedSlippage,
    savedSlippageValue,
    savedPriorityGas,
    savedOutputAmount,
    useAutoGas,
    useAutoSlippage,
    savedAddedPriority,
  } = useContext(BlockchainContext);

  // Core state
  const [sellAmount, setSellAmount] = useState(0);
  const [buyAmount, setBuyAmount] = useState("");
  const [swapData, setSwapData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isApprovalNeeded, setIsApprovalNeeded] = useState(false);

  // Refs for transaction state
  const inSwap = useRef(false);
  const blockNumberRef = useRef(0);

  // Quote fetching logic
  const fetchQuote = useCallback(async () => {
    if (!ALL_TOKENS[sellToken] || !ALL_TOKENS[buyToken] || !sellAmount || sellAmount === 0) {
      setSwapData(null);
      setBuyAmount("0");
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const sellTokenData = ALL_TOKENS[sellToken];
      const buyTokenData = ALL_TOKENS[buyToken];
      const sellTokenDecimals = sellTokenData?.decimals;
      
      const parsedSellAmount = ethers.parseUnits(sellAmount.toString(), sellTokenDecimals);
      
      // Determine swap type
      const swapType = getSwapType(sellTokenData.symbol, buyTokenData.symbol);
      
      // Get best quote based on swap type
      let quoteResult;
      switch (swapType) {
        case 'ETH_TO_TOKEN':
          quoteResult = await getEthToTokenQuote(parsedSellAmount, buyTokenData);
          break;
        case 'TOKEN_TO_ETH':
          quoteResult = await getTokenToEthQuote(parsedSellAmount, sellTokenData);
          break;
        case 'TOKEN_TO_TOKEN':
          quoteResult = await getTokenToTokenQuote(parsedSellAmount, sellTokenData, buyTokenData);
          break;
        default:
          throw new Error('Invalid swap type');
      }

      if (quoteResult) {
        // Format and set the buy amount
        const formattedBuyAmount = formatBuyAmount(quoteResult.bestQuote, buyTokenData.decimals);
        setBuyAmount(formattedBuyAmount);
        updateData("savedOutputAmount", formattedBuyAmount);

        // Create swap data object
        const newSwapData = {
          ...quoteResult,
          sellToken: sellTokenData,
          buyToken: buyTokenData,
          sellAmount: parsedSellAmount,
          buyAmount: quoteResult.bestQuote,
          swapType,
        };
        
        setSwapData(newSwapData);
        
        // Check if approval is needed
        await checkApprovalNeeded(sellTokenData, parsedSellAmount);
      }
    } catch (err) {
      console.error('Quote fetch failed:', err);
      setError(err.message);
      setSwapData(null);
      setBuyAmount("0");
    } finally {
      setLoading(false);
    }
  }, [sellAmount, sellToken, buyToken, ALL_TOKENS, chain_id, authToken]);

  // Helper functions
  const getSwapType = (sellSymbol, buySymbol) => {
    const nativeSymbol = 'ETH'; // This should come from config
    
    if (sellSymbol === nativeSymbol && buySymbol !== nativeSymbol) {
      return 'ETH_TO_TOKEN';
    } else if (sellSymbol !== nativeSymbol && buySymbol === nativeSymbol) {
      return 'TOKEN_TO_ETH';
    } else {
      return 'TOKEN_TO_TOKEN';
    }
  };

  const formatBuyAmount = (amount, decimals) => {
    try {
      const formatted = ethers.formatUnits(amount, decimals);
      const num = parseFloat(formatted);
      return num.toFixed(8);
    } catch (error) {
      console.error('Error formatting buy amount:', error);
      return "0";
    }
  };

  const checkApprovalNeeded = async (sellTokenData, amount) => {
    // Implementation for checking if approval is needed
    // This would check the allowance and compare with amount
    try {
      if (sellTokenData.symbol === 'ETH') {
        setIsApprovalNeeded(false);
        return;
      }
      
      // Check allowance logic here
      // For now, simplified logic
      setIsApprovalNeeded(true);
    } catch (error) {
      console.error('Error checking approval:', error);
      setIsApprovalNeeded(false);
    }
  };

  // Quote fetching functions (simplified versions)
  const getEthToTokenQuote = async (amount, buyTokenData) => {
    // Implementation for ETH to Token quotes
    // This would call both V2 and V3 and return the best
    return { bestQuote: amount, isV2Only: true }; // Placeholder
  };

  const getTokenToEthQuote = async (amount, sellTokenData) => {
    // Implementation for Token to ETH quotes
    return { bestQuote: amount, isV2Only: true }; // Placeholder
  };

  const getTokenToTokenQuote = async (amount, sellTokenData, buyTokenData) => {
    // Implementation for Token to Token quotes
    return { bestQuote: amount, isV2Only: true }; // Placeholder
  };

  return {
    // State
    sellAmount,
    setSellAmount,
    buyAmount,
    setBuyAmount,
    swapData,
    loading,
    error,
    isApprovalNeeded,
    
    // Actions
    fetchQuote,
    
    // Refs
    inSwap,
  };
};
