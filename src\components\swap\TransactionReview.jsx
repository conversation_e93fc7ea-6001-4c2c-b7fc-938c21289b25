"use client";

import React, { useState } from "react";
import "../../styles/transaction-review.css";

const TransactionReview = ({
  isOpen,
  onClose,
  fromToken,
  toToken,
  fromAmount,
  toAmount,
  fromUsdValue,
  toUsdValue,
  swapData,
  onConfirm,
  savedSlippageValue,
  savedAddedPriority,
  provider,
  chainId,
  pendingTxStatus,
  transaction,
  inSwap,
  devMode = false, // Add dev mode prop
}) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [isProcessing, setIsProcessing] = useState(false);

  if (!isOpen) return null;

  const handleStartApproval = async () => {
    if (devMode) {
      // In dev mode, just cycle through steps (only 2 steps now)
      if (currentStep < 2) {
        setCurrentStep(currentStep + 1);
      } else {
        setCurrentStep(1);
      }
      return;
    }

    if (!swapData || !onConfirm) return;

    setIsProcessing(true);
    setCurrentStep(1);

    try {
      // Call the original swap confirmation function
      await onConfirm();
      setCurrentStep(2);
    } catch (error) {
      console.error("Approval failed:", error);
      setIsProcessing(false);
    }
  };

  const handleStepClick = (step) => {
    if (devMode) {
      setCurrentStep(step);
    }
  };

  const getStepState = (stepNumber) => {
    if (stepNumber < currentStep) {
      return "completed";
    } else if (stepNumber === currentStep) {
      return "active";
    } else {
      return "pending";
    }
  };

  // Format USD values
  const formatUsdValue = (value) => {
    if (!value) return "0";
    return typeof value === "string"
      ? value.replace(/,/g, "")
      : value.toString();
  };

  // Format token amounts for display
  const formatTokenAmount = (amount) => {
    if (!amount || amount === "0") return "0";
    const num = parseFloat(amount);
    if (isNaN(num)) return "0";

    // For very small amounts, show more decimals
    if (num < 0.001) {
      return num.toFixed(8);
    }
    // For larger amounts, show fewer decimals
    if (num >= 1) {
      return num.toFixed(4);
    }
    // For medium amounts, show 6 decimals
    return num.toFixed(6);
  };

  return (
    <div className="transaction-review-overlay">
      <button onClick={onClose} className="transaction-close-button">
        ×
      </button>{" "}
      <div className="transaction-review-container">
        <div className="transaction-review-header">
          <div className="transaction-review-title">Review limit</div>
          <div className="transaction-get-help">Get help</div>
        </div>

        <div className="transaction-token-section-border">
          <div className="transaction-token-section">
            <div className="transaction-token-left">
              <div className="transaction-token-label">You pay</div>
            </div>
            <div className="transaction-token-right">
              <div className="transaction-token-right-inner">
                <div className="transaction-token-amount">
                  {formatTokenAmount(fromAmount)} {fromToken?.symbol || "ETH"}
                </div>
                <div className="transaction-token-usd">
                  ≈ ${formatUsdValue(fromUsdValue)}
                </div>
              </div>{" "}
              <div className="transaction-token-icon">
                <img
                  src={
                    fromToken?.logoURI ||
                    fromToken?.logo_uri ||
                    "https://placehold.co/28x28"
                  }
                  alt={fromToken?.symbol || "Token"}
                />
              </div>{" "}
            </div>
          </div>

          {/* You Receive Section */}
          <div className="transaction-token-section t-b">
            <div className="transaction-token-left">
              <div className="transaction-token-label">You receive</div>
            </div>
            <div className="transaction-token-right">
              <div className="transaction-token-right-inner">
                <div className="transaction-token-amount">
                  {formatTokenAmount(toAmount)} {toToken?.symbol || "AAVE"}
                </div>
                <div className="transaction-token-usd">
                  ≈ ${formatUsdValue(toUsdValue)}
                </div>
              </div>{" "}
              <div className="transaction-token-icon">
                <img
                  src={
                    toToken?.logoURI ||
                    toToken?.logo_uri ||
                    "https://placehold.co/28x28"
                  }
                  alt={toToken?.symbol || "Token"}
                />
              </div>{" "}
            </div>
          </div>
        </div>
        {/* Approval Process Section */}
        <div className="transaction-approval-section">
          {/* Step 1: Approve in wallet */}
          <div
            className={`transaction-approval-step ${getStepState(1)}`}
            onClick={() => handleStepClick(1)}
            style={{ cursor: devMode ? "pointer" : "default" }}
          >
            <div className="transaction-approval-step-icon">
              <img
                src={
                  fromToken?.logoURI ||
                  fromToken?.logo_uri ||
                  "https://placehold.co/20x20"
                }
                alt="Token"
                style={{
                  width: "28px",
                  height: "28px",
                  borderRadius: "50%",
                }}
              />
            </div>
            <div className="transaction-approval-step-content">
              <div className="transaction-approval-step-title">
                Approve in wallet
              </div>
              <div className="transaction-approval-help">
                Why do I have to approve a token?
              </div>
            </div>
          </div>

          {/* Step 2: Confirm swap */}
          <div
            className={`transaction-approval-step ${getStepState(2)}`}
            onClick={() => handleStepClick(2)}
            style={{ cursor: devMode ? "pointer" : "default" }}
          >
            <div className="transaction-approval-step-icon">
              <span style={{ color: "white", fontSize: "16px" }}>✓</span>
            </div>
            <div className="transaction-approval-step-content">
              <div className="transaction-approval-step-title">
                Confirm swap
              </div>
            </div>
          </div>
        </div>

        {/* Action Button */}
        <button
          onClick={handleStartApproval}
          disabled={!devMode && (isProcessing || inSwap)}
          className="transaction-action-button"
        >
          {devMode
            ? `Dev Mode - Step ${currentStep}/2 (Click to cycle)`
            : isProcessing || inSwap
            ? "Processing..."
            : "Start Approval"}
        </button>

        {/* Dev Mode Controls */}
      </div>
    </div>
  );
};

export default TransactionReview;
