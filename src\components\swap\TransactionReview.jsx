"use client";

import React, { useState } from "react";
import "../../styles/transaction-review.css";

const TransactionReview = ({
  isOpen,
  onClose,
  fromToken,
  toToken,
  fromAmount,
  toAmount,
  fromUsdValue,
  toUsdValue,
  swapData,
  onConfirm,
  savedSlippageValue,
  savedAddedPriority,
  provider,
  chainId,
  pendingTxStatus,
  transaction,
  inSwap,
  devMode = false, // Add dev mode prop
}) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [isProcessing, setIsProcessing] = useState(false);

  if (!isOpen) return null;

  const handleStartApproval = async () => {
    if (devMode) {
      // In dev mode, just cycle through steps (only 2 steps now)
      if (currentStep < 2) {
        setCurrentStep(currentStep + 1);
      } else {
        setCurrentStep(1);
      }
      return;
    }

    if (!swapData || !onConfirm) return;

    setIsProcessing(true);
    setCurrentStep(1);

    try {
      // Call the original swap confirmation function
      await onConfirm();
      setCurrentStep(2);
    } catch (error) {
      console.error("Approval failed:", error);
      setIsProcessing(false);
    }
  };

  const handleStepClick = (step) => {
    if (devMode) {
      setCurrentStep(step);
    }
  };

  const getStepState = (stepNumber) => {
    if (stepNumber < currentStep) {
      return "completed";
    } else if (stepNumber === currentStep) {
      return "active";
    } else {
      return "pending";
    }
  };

  // Format USD values
  const formatUsdValue = (value) => {
    if (!value) return "0";
    return typeof value === "string"
      ? value.replace(/,/g, "")
      : value.toString();
  };

  return (
    <div className="transaction-review-overlay">
      <div className="transaction-review-container">
        {/* Header */}
        <div className="transaction-review-header">
          <div className="transaction-review-title">Review limit</div>
        </div>

        {/* Close Button */}
        <button onClick={onClose} className="transaction-close-button">
          ×
        </button>

        <div className="transaction-token-section-border">
          <div className="transaction-token-section">
            <div className="transaction-token-left">
              <div className="transaction-token-label">You pay</div>
            </div>
            <div className="transaction-token-right">
              <div className="transaction-token-right-inner">
                <div className="transaction-token-amount">
                  {fromAmount} {fromToken?.symbol || "ETH"}
                </div>
                <div className="transaction-token-usd">
                  ≈ ${formatUsdValue(fromUsdValue)}
                </div>
              </div>{" "}
              <div className="transaction-token-icon">
                <img
                  src={fromToken?.logoURI || "https://placehold.co/28x28"}
                  alt={fromToken?.symbol || "Token"}
                />
              </div>{" "}
            </div>
          </div>

          {/* You Receive Section */}
          <div className="transaction-token-section">
            <div className="transaction-token-left">
              <div className="transaction-token-label">You receive</div>
            </div>
            <div className="transaction-token-right">
              <div className="transaction-token-amount">
                {toAmount} {toToken?.symbol || "AAVE"}
              </div>
              <div className="transaction-token-usd">
                ≈ ${formatUsdValue(toUsdValue)}
              </div>
            </div>
            <div className="transaction-token-icon">
              <img
                src={toToken?.logoURI || "https://placehold.co/28x28"}
                alt={toToken?.symbol || "Token"}
              />
            </div>
          </div>
        </div>
        {/* Approval Process Section */}
        <div className="transaction-approval-section">
          {/* Step 1: Approve in wallet */}
          <div
            className={`transaction-approval-step ${getStepState(1)}`}
            onClick={() => handleStepClick(1)}
            style={{ cursor: devMode ? "pointer" : "default" }}
          >
            <div className="transaction-approval-step-icon">
              <img
                src={fromToken?.logoURI || "https://placehold.co/20x20"}
                alt="Token"
                style={{
                  width: "20px",
                  height: "20px",
                  borderRadius: "50%",
                }}
              />
            </div>
            <div className="transaction-approval-step-content">
              <div className="transaction-approval-step-title">
                Approve in wallet
              </div>
              <div className="transaction-approval-help">
                Why do I have to approve a token?
              </div>
            </div>
          </div>

          {/* Step 2: Confirm swap */}
          <div
            className={`transaction-approval-step ${getStepState(2)}`}
            onClick={() => handleStepClick(2)}
            style={{ cursor: devMode ? "pointer" : "default" }}
          >
            <div className="transaction-approval-step-icon">
              <span style={{ color: "white", fontSize: "16px" }}>✓</span>
            </div>
            <div className="transaction-approval-step-content">
              <div className="transaction-approval-step-title">
                Confirm swap
              </div>
            </div>
          </div>
        </div>

        {/* Action Button */}
        <button
          onClick={handleStartApproval}
          disabled={!devMode && (isProcessing || inSwap)}
          className="transaction-action-button"
        >
          {devMode
            ? `Dev Mode - Step ${currentStep}/2 (Click to cycle)`
            : isProcessing || inSwap
            ? "Processing..."
            : "Start Approval"}
        </button>

        {/* Dev Mode Controls */}
        {devMode && (
          <div
            style={{
              marginTop: "16px",
              padding: "12px",
              background: "#FFE4B5",
              borderRadius: "8px",
              border: "1px solid #DDD",
              fontSize: "12px",
              color: "#333",
            }}
          >
            <div style={{ fontWeight: "bold", marginBottom: "8px" }}>
              🔧 Dev Mode Active - Persistent on Refresh
            </div>
            <div>• Click any step to jump to it</div>
            <div>• Click the button to cycle through steps</div>
            <div>• Modal stays open during development</div>
            <div>• Current step: {currentStep}/2</div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TransactionReview;
