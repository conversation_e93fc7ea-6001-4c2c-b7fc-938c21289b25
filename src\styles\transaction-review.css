/* Transaction Review Modal Styles */

.transaction-review-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.transaction-review-container {
  width: 416px;
  position: relative;
  padding: 15px;
  background: #f0efe6;
  border-radius: 38px;
  border: 1px solid var(--border);
  backdrop-filter: blur(22px);
  background: var(--box-bg);
}
.light .transaction-review-container {
  background: var(--light-gray);
  border: 2px solid var(--light-border);
}

/* Header */
.transaction-review-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-inline: 20px;
  padding-top: 8px;
  padding-bottom: 1px;
  font-size: 16px;
}

.transaction-review-title {
  color: var(--text);
  font-weight: 500;
  line-height: 18px;
}
.light .transaction-review-title {
  color: #000;
}

.transaction-get-help {
  color: #808080;
  font-weight: 400;
  line-height: 18px;
  cursor: pointer;
}

.transaction-close-button {
  position: absolute;
  top: 40px;
  right: 40px;
  background: #000000;
  border: none;
  font-size: 50px;
  font-weight: 100;
  cursor: pointer;
  color: #ffffff;
  width: 70px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.transaction-close-button:hover {
  background: #333333;
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
}

.transaction-close-button:active {
  transform: scale(0.95);
}

/* Token Sections */
.transaction-token-section {
  border-radius: 28px;
  padding: 20px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.transaction-token-section.t-b {
  position: relative;
}

.transaction-token-section.t-b::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--border);
}
.light .transaction-token-section.t-b::before {
  background: var(--light-border);
}

.transaction-token-section-border {
  border: 1px solid var(--border);
  background: var(--box-inner);
  border-radius: 28px;
  margin-bottom: 16px;
}
.light .transaction-token-section-border {
  background: var(--light-weak-grey);
  border: 2px solid var(--light-border);
}

.transaction-token-left {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.transaction-token-label {
  color: var(--text);
  font-size: 16px;
  font-weight: 400;
  line-height: 18px;
}
.light .transaction-token-label {
  color: #000;
}

.transaction-token-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.transaction-token-right-inner {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.transaction-token-amount {
  color: var(--text);
  font-size: 20px;
  font-weight: 400;
  line-height: 30px;
  text-align: right;
}
.light .transaction-token-amount {
  color: #000;
}

.transaction-token-usd {
  color: #808080;
  font-size: 16px;
  font-weight: 400;
  line-height: 18px;
  text-align: right;
}

.transaction-token-icon {
  width: 48px;
  height: 48px;
  background: #e8e6db;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.transaction-token-icon img {
  width: 28px;
  height: 28px;
  border-radius: 50%;
}

/* Approval Process Section */
.transaction-approval-section {
  background: var(--box-inner);
  border: 1px solid var(--border);
  border-radius: 28px;
  padding: 24px;
  margin-bottom: 24px;
}
.light .transaction-approval-section {
  background: var(--light-weak-grey);
  border: 2px solid var(--light-border);
}

.transaction-approval-step {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  position: relative;
}

.transaction-approval-step:last-child {
  margin-bottom: 0;
}

.transaction-approval-step-icon {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  flex-shrink: 0;
}

.transaction-approval-step-content {
  flex: 1;
}

.transaction-approval-step-title {
  color: var(--text);
  font-size: 14px;
  font-weight: 400;
  line-height: 18px;
  margin-bottom: 4px;
}
.light .transaction-approval-step-title {
  color: #000;
}

.transaction-approval-help {
  color: var(--text-gray);
  font-size: 10px;
  font-weight: 400;
  text-decoration: underline;
  line-height: 6px;
  cursor: pointer;
}
.light .transaction-approval-help {
  color: var(--light-text-grey);
}

/* Step States */
.transaction-approval-step.active .transaction-approval-step-icon {
  background: var(--primary-color, #fce025);
}

.transaction-approval-step.completed .transaction-approval-step-icon {
  background: var(--primary-color, #fce025);
}

.transaction-approval-step.pending .transaction-approval-step-icon {
  background: #9a9a9a;
}

/* Connecting Lines */
.transaction-approval-step:not(:last-child)::after {
  content: "";
  position: absolute;
  left: 19px;
  top: 40px;
  width: 2px;
  height: 24px;
  background: #9a9a9a;
}

/* Action Button */
.transaction-action-button {
  width: 100%;
  padding: 12px 24px;
  background: var(--primary-color, #fce025);
  color: #000;
  border: none;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  font-family: "Poppins", sans-serif;
  transition: opacity 0.2s ease;
}

.transaction-action-button:disabled {
  background: #ccc;
  cursor: not-allowed;
  opacity: 0.6;
}

/* Interactive States */
.transaction-get-help:hover {
  color: var(--primary-color);
  text-decoration: underline;
}

.transaction-approval-help:hover {
  color: var(--primary-color);
}

/* Mobile Responsive */
@media (max-width: 480px) {
  .transaction-review-container {
    width: 90vw;
    max-width: 380px;
    margin: 20px;
  }

  .transaction-token-amount {
    font-size: 18px;
  }
}
