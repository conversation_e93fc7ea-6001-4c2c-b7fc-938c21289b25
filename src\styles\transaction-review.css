/* Transaction Review Modal Styles */

.transaction-review-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.transaction-review-container {
  width: 416px;
  position: relative;
  padding: 24px;
  background: #f0efe6;
  border-radius: 38px;
  border: 1px solid #d8d7cf;
  backdrop-filter: blur(22px);
  background: var(--box-bg);
}

/* Header */
.transaction-review-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.transaction-review-title {
  color: #16171a;
  font-size: 14px;
  font-weight: 500;
  line-height: 18px;
}

.transaction-get-help {
  color: #808080;
  font-size: 14px;
  font-weight: 400;
  line-height: 18px;
  cursor: pointer;
}

.transaction-close-button {
  position: absolute;
  top: 20px;
  right: 20px;
  background: transparent;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #808080;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

/* Token Sections */
.transaction-token-section {
  border-radius: 28px;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.transaction-token-section-border {
  border: 1px solid var(--border);
  border-radius: 28px;
}

.transaction-token-left {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.transaction-token-label {
  color: #000;
  font-size: 14px;
  font-weight: 400;
  line-height: 18px;
}

.transaction-token-right {
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  gap: 4px;
}
.transaction-token-right-inner {
  flex-direction: column;
  align-items: flex-end;
}

.transaction-token-amount {
  color: #16171a;
  font-size: 20px;
  font-weight: 400;
  line-height: 30px;
  text-align: right;
}

.transaction-token-usd {
  color: #808080;
  font-size: 12px;
  font-weight: 400;
  line-height: 18px;
  text-align: right;
}

.transaction-token-icon {
  width: 48px;
  height: 48px;
  background: var(--box-inner);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.transaction-token-icon img {
  width: 28px;
  height: 28px;
  border-radius: 50%;
}

/* Approval Process Section */
.transaction-approval-section {
  background: #f7f6f0;
  border-radius: 28px;
  border: 1px solid #d8d7cf;
  padding: 24px;
  margin-bottom: 24px;
}

.transaction-approval-step {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  position: relative;
}

.transaction-approval-step:last-child {
  margin-bottom: 0;
}

.transaction-approval-step-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  flex-shrink: 0;
}

.transaction-approval-step-content {
  flex: 1;
}

.transaction-approval-step-title {
  color: #000;
  font-size: 14px;
  font-weight: 400;
  line-height: 18px;
  margin-bottom: 4px;
}

.transaction-approval-help {
  color: #000;
  font-size: 8px;
  font-weight: 400;
  text-decoration: underline;
  line-height: 10.4px;
  cursor: pointer;
}

/* Step States */
.transaction-approval-step.active .transaction-approval-step-icon {
  background: var(--primary-color, #fce025);
}

.transaction-approval-step.completed .transaction-approval-step-icon {
  background: var(--primary-color, #fce025);
}

.transaction-approval-step.pending .transaction-approval-step-icon {
  background: #9a9a9a;
}

/* Connecting Lines */
.transaction-approval-step:not(:last-child)::after {
  content: "";
  position: absolute;
  left: 19px;
  top: 40px;
  width: 2px;
  height: 24px;
  background: #9a9a9a;
}

/* Action Button */
.transaction-action-button {
  width: 100%;
  padding: 12px 24px;
  background: var(--primary-color, #fce025);
  color: #000;
  border: none;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  font-family: "Poppins", sans-serif;
  transition: opacity 0.2s ease;
}

.transaction-action-button:disabled {
  background: #ccc;
  cursor: not-allowed;
  opacity: 0.6;
}

/* Dark Mode Styles */
.dark .transaction-review-container {
  background: var(--box-inner);
  border-color: var(--border);
}

.dark .transaction-token-section,
.dark .transaction-approval-section {
  background: var(--box-inner);
  border-color: var(--border);
}

.dark .transaction-review-title,
.dark .transaction-token-label,
.dark .transaction-approval-step-title,
.dark .transaction-token-amount {
  color: var(--text);
}

.dark .transaction-token-icon {
  background: var(--box-darker);
}

/* Interactive States */
.transaction-get-help:hover {
  color: var(--primary-color);
  text-decoration: underline;
}

.transaction-approval-help:hover {
  color: var(--primary-color);
}

/* Mobile Responsive */
@media (max-width: 480px) {
  .transaction-review-container {
    width: 90vw;
    max-width: 380px;
    margin: 20px;
  }

  .transaction-token-amount {
    font-size: 18px;
  }
}
