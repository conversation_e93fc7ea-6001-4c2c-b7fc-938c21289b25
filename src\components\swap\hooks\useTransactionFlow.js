// Custom hook for transaction flow management
import { useState, useRef, useCallback } from 'react';

export const useTransactionFlow = () => {
  // Transaction state
  const [showTransactionFlow, setShowTransactionFlow] = useState(false);
  const [pendingTxStatus, setPendingTxStatus] = useState(null);
  const [currentStep, setCurrentStep] = useState(1);
  const [isProcessing, setIsProcessing] = useState(false);

  // Transaction data refs
  const transactionDataRef = useRef({
    sellAmount: "0",
    buyAmount: "0",
    sellUsdValue: "0",
    buyUsdValue: "0",
    swapData: null,
    isApprovalNeeded: false,
    handleSwap: null,
    handleApprove: null,
  });

  const inSwap = useRef(false);

  // Open transaction flow
  const openTransactionFlow = useCallback((transactionData) => {
    console.log('Opening transaction flow with data:', transactionData);
    
    // Store transaction data
    transactionDataRef.current = {
      ...transactionDataRef.current,
      ...transactionData,
    };

    // Set initial step based on approval needs
    setCurrentStep(transactionData.isApprovalNeeded ? 1 : 2);
    setShowTransactionFlow(true);
    setPendingTxStatus(null);
    setIsProcessing(false);
  }, []);

  // Close transaction flow
  const closeTransactionFlow = useCallback(() => {
    console.log('Closing transaction flow');
    setShowTransactionFlow(false);
    setPendingTxStatus(null);
    setCurrentStep(1);
    setIsProcessing(false);
    inSwap.current = false;
    
    // Clear transaction data
    transactionDataRef.current = {
      sellAmount: "0",
      buyAmount: "0",
      sellUsdValue: "0",
      buyUsdValue: "0",
      swapData: null,
      isApprovalNeeded: false,
      handleSwap: null,
      handleApprove: null,
    };
  }, []);

  // Execute approval step
  const executeApproval = useCallback(async () => {
    const { handleApprove, isApprovalNeeded } = transactionDataRef.current;
    
    if (!isApprovalNeeded || !handleApprove) {
      console.warn('Approval not needed or handler not available');
      return;
    }

    setIsProcessing(true);
    setCurrentStep(1);

    try {
      console.log('Starting approval...');
      await handleApprove();
      
      // Move to next step after successful approval
      setCurrentStep(2);
      console.log('Approval completed, moving to step 2');
    } catch (error) {
      console.error('Approval failed:', error);
      setIsProcessing(false);
      throw error;
    }
  }, []);

  // Execute swap step
  const executeSwap = useCallback(async () => {
    const { handleSwap } = transactionDataRef.current;
    
    if (!handleSwap) {
      console.warn('Swap handler not available');
      return;
    }

    setIsProcessing(true);
    setCurrentStep(2);
    inSwap.current = true;

    try {
      console.log('Starting swap...');
      setPendingTxStatus('pending');
      await handleSwap();
      
      console.log('Swap completed');
    } catch (error) {
      console.error('Swap failed:', error);
      setPendingTxStatus('failed');
      inSwap.current = false;
      setIsProcessing(false);
      throw error;
    }
  }, []);

  // Execute transaction (handles both approval and swap)
  const executeTransaction = useCallback(async () => {
    const { isApprovalNeeded } = transactionDataRef.current;

    try {
      if (isApprovalNeeded && currentStep === 1) {
        // Execute approval first
        await executeApproval();
        // Don't execute swap here - let user click again or auto-proceed
      } else {
        // Execute swap
        await executeSwap();
      }
    } catch (error) {
      console.error('Transaction execution failed:', error);
      // Error handling is done in individual functions
    }
  }, [currentStep, executeApproval, executeSwap]);

  // Update transaction status (called from external transaction handlers)
  const updateTransactionStatus = useCallback((status) => {
    console.log('Updating transaction status:', status);
    setPendingTxStatus(status);
    
    if (status === 'confirmed' || status === 'failed') {
      inSwap.current = false;
      setIsProcessing(false);
      
      // Auto-close after showing final status
      setTimeout(() => {
        closeTransactionFlow();
      }, 2000);
    }
  }, [closeTransactionFlow]);

  // Get current transaction data
  const getTransactionData = useCallback(() => {
    return transactionDataRef.current;
  }, []);

  // Get step state for UI
  const getStepState = useCallback((stepNumber) => {
    if (stepNumber < currentStep) {
      return 'completed';
    } else if (stepNumber === currentStep) {
      return 'active';
    } else {
      return 'pending';
    }
  }, [currentStep]);

  // Get button text based on current state
  const getButtonText = useCallback(() => {
    const { isApprovalNeeded } = transactionDataRef.current;

    if (pendingTxStatus === 'pending') {
      return 'Transaction Pending...';
    } else if (pendingTxStatus === 'confirmed') {
      return 'Transaction Confirmed!';
    } else if (pendingTxStatus === 'failed') {
      return 'Transaction Failed';
    } else if (isProcessing || inSwap.current) {
      return 'Processing...';
    } else if (isApprovalNeeded && currentStep === 1) {
      return 'Start Approval';
    } else {
      return 'Confirm Swap';
    }
  }, [pendingTxStatus, isProcessing, currentStep]);

  // Check if button should be disabled
  const isButtonDisabled = useCallback(() => {
    return isProcessing || inSwap.current || pendingTxStatus === 'pending';
  }, [isProcessing, pendingTxStatus]);

  return {
    // State
    showTransactionFlow,
    pendingTxStatus,
    currentStep,
    isProcessing,
    inSwap: inSwap.current,

    // Actions
    openTransactionFlow,
    closeTransactionFlow,
    executeTransaction,
    updateTransactionStatus,

    // Getters
    getTransactionData,
    getStepState,
    getButtonText,
    isButtonDisabled,

    // Step control
    setCurrentStep,
  };
};
