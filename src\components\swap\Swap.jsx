/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @next/next/no-img-element */

/* use client */
import { DownArrow } from "../common/SVGMAIN.js";

import SwitchCurrenciesButton from "../common/SwitchButton";
import SwapSettings from "./SwapSettings";
import { useState, useEffect, useContext, useRef, useMemo, use } from "react";
import { ethers, Interface } from "ethers";

import JuicedModeSettings from "./JuicedModeSettings";

import { BlockchainContext } from "../../context/BlockchainContext";
import { erc20Abi } from "viem";
import TokenList from "./TokenList";
import { toast } from "react-hot-toast";

import Iframe from "../custom/Iframe";
import routerABI from "../../constants/abis/router.json";
import wethABI from "../../constants/abis/wethABI.json";
import DollarValue from "../common/DollarValue";
import WalletIcon from "../common/svgs/WalletIcon";
import { CHAINS } from "../../constants/constants";
import TransactionReview from "./TransactionReview";
import fetchBlockNumber from "../../services/blockchain/fetchBlockNumber";
import getAmountOutV2 from "../../services/blockchain/getAmountOutV2.js";
import getUniswapQuoteV3 from "../../services/blockchain/getUniswapQuoteV3.js";

const Swap = ({ buyLink, buyLinkKey, ALL_TOKENS }) => {
  const {
    signer,
    provider,
    account,
    tokenListOpenRef,
    ETH_TOKENS,
    chain_id,
    saverInputAmount,
    authToken,
    isJuicedMode,
    trackSwapEvent,
  } = useContext(BlockchainContext);
  const nativeSymbol = CHAINS[chain_id].nativeSymbol;
  const isBSC = chain_id === 56;
  const isETH = chain_id === 1;
  function findKeyBySymbol(symbol) {
    let token = symbol;
    try {
      token = Object.keys(ALL_TOKENS).find(
        (key) => ALL_TOKENS[key].symbol === symbol
      );
    } catch (error) {
      console.warn("Error finding token by symbol:", error);
    }
    return token;
  }

  const feeAddress = "******************************************";
  const uniswapRouterAddress = CHAINS[chain_id].uniswapRouterAddressV2;
  const routerAddressV3 = CHAINS[chain_id].uniswapRouterAddressV3;
  const routerAddress = CHAINS[chain_id].pappleRouterAddress;
  const wethAddress = CHAINS[chain_id].wethAddress;
  const ethAddress = CHAINS[chain_id].ethAddress;
  const uniswapFactoryV2Address = CHAINS[chain_id].uniswapFactoryV2Address;

  let weth = wethAddress;
  const [isMobile, setIsMobile] = useState(false);
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener("resize", handleResize);
    handleResize();

    return () => window.removeEventListener("resize", handleResize);
  }, []);

  useEffect(() => {
    if (buyLink) {
      handleContractImport(buyLink);
    }
  }, [buyLink]);

  /*   useEffect(() => {
    if (isJuicedMode === true && showChart === false) {
      setShowChart(true);
    } else if (isJuicedMode === false && showChart === true) {
      setShowChart(false);
    }
  }, [isJuicedMode]); */

  function balanceDisplayFixer(balance) {
    if (!balance || balance === "0" || balance === "Error" || isNaN(balance)) {
      return "0";
    } else {
      return parseFloat(balance).toFixed(4);
    }
  }
  function handleAdChart(value) {
    setBuyToken(findKeyBySymbol(value));
    setShowChartState(true);
  }

  function getBuyToken() {
    if (!buyLink) {
      if (chain_id === 1) {
        return findKeyBySymbol("PAPPLE");
      } else {
        return Object.keys(ALL_TOKENS)[3];
      }
    } else {
      return buyLinkKey;
    }
  }

  const [priortyGas, setPriorityGas] = useState(0.1);

  const priorityGasRef = useRef(null);
  const slippageRef = useRef(null);
  const [buyToken, setBuyToken] = useState(getBuyToken());

  const [showChart, setShowChart] = useState(false);
  const [showAudits, setShowAudits] = useState();

  const [sellToken, setSellToken] = useState(findKeyBySymbol(nativeSymbol));
  const [sellTokenDisplayBalance, setSellTokenDisplayBalance] = useState(0);
  const [buyTokenDisplayBalance, setBuyTokenDisplayBalance] = useState(0);
  const [showTokenList, setShowTokenList] = useState(false);
  const [trigger, setTrigger] = useState(0);

  const [testState, setTestState] = useState(0);

  // Transaction Review state
  const [showTransactionFlow, setShowTransactionFlow] = useState(false);
  const [pendingTxStatus, setPendingTxStatus] = useState(null);

  // Refs to store USD values for TransactionReview
  const sellUsdValueRef = useRef("0");
  const buyUsdValueRef = useRef("0");

  // Refs to store swap data for TransactionReview
  const swapDataRef = useRef(null);
  const sellAmountRef = useRef("0");
  const buyAmountRef = useRef("0");

  const GWEI = useRef(0);
  const RATE_LIMIT = 1500;
  useEffect(() => {
    tokenListOpenRef.current = showTokenList;
  }, [showTokenList]);

  function setShowChartState(value) {
    setShowChart(value);
  }
  function setShowAuditsState(value) {
    setShowAudits(value);
  }
  function swapTokens() {
    const buyTokenSave = buyToken;
    const sellTokenSave = sellToken;
    setBuyToken(sellTokenSave);
    setSellToken(buyTokenSave);
  }

  function CurrentGwei() {
    const [CurrentGwei, setCurrentGwei] = useState(GWEI.current);

    async function getGasFees() {
      try {
        const feeData = await provider.getFeeData();

        const baseFeePerGas = feeData.gasPrice;

        const baseFeePerGasGwei = ethers.formatUnits(
          baseFeePerGas?.toString(),
          "gwei"
        );

        if (baseFeePerGasGwei !== GWEI.current) {
          GWEI.current = Number(baseFeePerGasGwei).toFixed(2);

          let currentFee = Number(baseFeePerGasGwei).toFixed(2);
          if (currentFee > 50) {
            currentFee = `${GWEI.current} High 🔴`;
          } else if (currentFee > 30) {
            currentFee = `${GWEI.current} High 🟠`;
          } else if (currentFee > 20) {
            currentFee = `${GWEI.current} Average 🟡`;
          } else {
            currentFee = `${GWEI.current} Low 🟢`;
          }

          setCurrentGwei(currentFee);
        }
      } catch (error) {}
    }

    useEffect(() => {
      const interval = setInterval(() => {
        getGasFees();
      }, 3000);

      return () => clearInterval(interval);
    }, []);
    return CurrentGwei;
  }

  useEffect(() => {
    async function updateTokenBalances() {
      if (account && authToken && sellToken && buyToken) {
        console.log(
          "Fetching token balances with authToken:",
          authToken ? "available" : "not available"
        );

        try {
          const sellTokenBalance = await getTokenBalance(sellToken);
          const buyTokenBalance = await getTokenBalance(buyToken);

          setSellTokenDisplayBalance(sellTokenBalance);
          setBuyTokenDisplayBalance(buyTokenBalance);
        } catch (error) {
          console.warn("Error updating token balances:", error);

          setSellTokenDisplayBalance("0");
          setBuyTokenDisplayBalance("0");
        }
      } else {
        setSellTokenDisplayBalance("0");
        setBuyTokenDisplayBalance("0");
      }
    }

    updateTokenBalances();
  }, [account, authToken, sellToken, buyToken, trigger]);

  async function getTokenBalance(tokenKey) {
    try {
      if (!account || !authToken || !tokenKey) {
        console.log(
          "Missing account, authToken, or tokenKey for balance fetch"
        );
        return "0";
      }

      const token = ALL_TOKENS[tokenKey];
      if (!token) {
        console.log(`Token with key ${tokenKey} not found`);
        return "0";
      }

      const tokenAddress = token.address;
      let balance;

      if (token.symbol === nativeSymbol) {
        try {
          const response = await fetch("/api/rpc-call/get-balance", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${authToken}`,
            },
            body: JSON.stringify({ chain_id, account }),
          });

          if (!response.ok) {
            console.warn("Error fetching balance:", response.status);
            return "0";
          }

          const data = await response.json();
          balance = data.balance;
        } catch (error) {
          console.warn("Failed to fetch balance:", error);
          return "0";
        }
      } else {
        try {
          const response = await fetch("/api/rpc-call/get-token-balance", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${authToken}`,
            },
            body: JSON.stringify({ chain_id, account, tokenAddress }),
          });

          if (!response.ok) {
            console.warn("Error fetching token balance:", response.status);
            return "0";
          }

          const data = await response.json();
          balance = data.tokenBalance;
        } catch (error) {
          console.warn("Failed to fetch token balance:", error);
          return "0";
        }
      }

      if (!balance) {
        return "0";
      }

      try {
        const formattedBalance = ethers.formatUnits(balance, token.decimals);
        return Number(formattedBalance);
      } catch (error) {
        console.warn("Error formatting balance:", error);
        return "0";
      }
    } catch (error) {
      console.warn("Error getting token balance:", error);
      return "0";
    }
  }

  function handlePriorityGasChange(value) {
    if (value > 100) {
      value = 100;
    } else if (value < 0) {
      value = 0;
    }
    setPriorityGas(value);
    priorityGasRef.current.value = value;
  }
  function handleSlippageChange(value) {
    if (value > 100) {
      value = 100;
    } else if (value < 0) {
      value = 0;
    }

    updateData("savedSlippage", value.toString());
    updateData("savedSlippageValue", parseFloat(value));
    slippageRef.current.value = value;
  }
  const handleShowTokenList = (type) => {
    setShowTokenList(type);
  };
  const handleSellTokenChange = (key, isPort) => {
    setSellToken(key);
    if (isPort === true) {
      setBuyToken(findKeyBySymbol(nativeSymbol));
      setMainTab("Swap");
      setSubTab("Trade");
    }
  };
  const handleBuyTokenChange = (key, isPort) => {
    setBuyToken(key);
    if (isPort === true) {
      setSellToken(findKeyBySymbol(nativeSymbol));
      setMainTab("Swap");
      setSubTab("Trade");
    }
  };

  function YouPay({ setShowTokenList, ALL_TOKENS, sellTokenDisplayBalance }) {
    const { updateData, account } = useContext(BlockchainContext);
    updateData("savedInputAmount", 0);
    saverInputAmount.current = 0;

    const inputRef = useRef(null);
    const [activePercentage, setActivePercentage] = useState(null);

    function handleInputAmountChange(value) {
      try {
        if (!account) {
          updateData("savedInputAmount", 0);
          saverInputAmount.current = 0;
          if (inputRef.current) {
            inputRef.current.value = "";
          }
          setActivePercentage(null);
          return;
        }

        const numValue = parseFloat(value);
        if (isNaN(numValue)) {
          updateData("savedInputAmount", 0);
          setActivePercentage(null);
          return;
        }

        updateData("savedInputAmount", value);
      } catch (error) {
        console.warn("Error in handleInputAmountChange:", error);
        // Ensure we don't crash even if there's an error
        try {
          updateData("savedInputAmount", 0);
          if (inputRef.current) {
            inputRef.current.value = "";
          }
        } catch (innerError) {
          console.warn("Error in error recovery:", innerError);
        }
      }
    }

    function handlePercentageClick(percentage) {
      setActivePercentage(percentage);
      let amount;

      switch (percentage) {
        case 25:
          amount = parseFloat(sellTokenDisplayBalance) * 0.25;
          break;
        case 50:
          amount = parseFloat(sellTokenDisplayBalance) * 0.5;
          break;
        case 75:
          amount = parseFloat(sellTokenDisplayBalance) * 0.75;
          break;
        case 100:
          amount = parseFloat(sellTokenDisplayBalance);
          break;
        default:
          amount = 0;
      }

      inputRef.current.value = amount.toFixed(6);
      handleInputAmountChange(amount.toFixed(6));
    }

    function handleMax() {
      inputRef.current.value = sellTokenDisplayBalance;
      updateData("savedInputAmount", sellTokenDisplayBalance);
      setActivePercentage(100);
    }

    return (
      <div className="flex-col">
        <div className="token-input-box">
          <div className="token-input-box-top">
            <div className="small-text">Sell</div>

            <div
              className="token-select-box"
              onClick={() => setShowTokenList("sellToken")}
            >
              <img
                src={ALL_TOKENS[sellToken]?.logo_uri}
                alt={ALL_TOKENS[sellToken]?.name}
                width={25}
                height={25}
                style={{ borderRadius: "50%" }}
              />{" "}
              <div className="med-text">{ALL_TOKENS[sellToken]?.symbol}</div>
              <DownArrow />{" "}
            </div>
          </div>
          <div className="flex-row bt">
            <input
              className="token-input"
              placeholder="0"
              type="number"
              ref={inputRef}
              onChange={(e) => handleInputAmountChange(e.target.value)}
            />
            <div className="percentage-buttons">
              <button
                className="percentage-button"
                onClick={() => {
                  try {
                    if (!account) {
                      toast("Please connect your wallet");
                      return;
                    }
                    const amount = parseFloat(sellTokenDisplayBalance) * 0.25;
                    inputRef.current.value = amount.toFixed(6);
                    handleInputAmountChange(amount.toFixed(6));
                  } catch (error) {
                    console.warn("Error in 25% button:", error);
                  }
                }}
              >
                25%
              </button>
              <button
                className="percentage-button"
                onClick={() => {
                  try {
                    if (!account) {
                      toast("Please connect your wallet");
                      return;
                    }
                    const amount = parseFloat(sellTokenDisplayBalance) * 0.5;
                    inputRef.current.value = amount.toFixed(6);
                    handleInputAmountChange(amount.toFixed(6));
                  } catch (error) {
                    console.warn("Error in 50% button:", error);
                  }
                }}
              >
                50%
              </button>
              <button
                className="percentage-button"
                onClick={() => {
                  try {
                    if (!account) {
                      toast("Please connect your wallet");
                      return;
                    }
                    const amount = parseFloat(sellTokenDisplayBalance) * 0.75;
                    inputRef.current.value = amount.toFixed(6);
                    handleInputAmountChange(amount.toFixed(6));
                  } catch (error) {
                    console.warn("Error in 75% button:", error);
                  }
                }}
              >
                75%
              </button>
              <button
                className="percentage-button"
                onClick={() => {
                  try {
                    if (!account) {
                      toast("Please connect your wallet");
                      return;
                    }
                    inputRef.current.value = sellTokenDisplayBalance;
                    handleInputAmountChange(sellTokenDisplayBalance);
                  } catch (error) {
                    console.warn("Error in 100% button:", error);
                  }
                }}
              >
                100%
              </button>
            </div>
          </div>
          <div className="flex-row">
            <div className="small-text">
              {" "}
              <DollarValue
                Token={ALL_TOKENS[sellToken]}
                isOutputToken={false}
                onValueChange={(value) => {
                  sellUsdValueRef.current = value;
                }}
              />
            </div>
            <div className="small-text">
              <div className="max-row">
                {/*          <div className="max-button" onClick={handleMax}>
                  MAX
                </div> */}
                <WalletIcon />
                {balanceDisplayFixer(sellTokenDisplayBalance)}{" "}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
  const memoYouPay = useMemo(() => {
    return (
      <YouPay
        setShowTokenList={setShowTokenList}
        ALL_TOKENS={ALL_TOKENS}
        sellTokenDisplayBalance={sellTokenDisplayBalance}
      />
    );
  }, [setShowTokenList, ALL_TOKENS, sellTokenDisplayBalance]);

  function YouReceive({
    setShowTokenList,
    ALL_TOKENS,
    buyTokenDisplayBalance,
  }) {
    const { savedOutputAmount, savedInputAmount } =
      useContext(BlockchainContext);
    const [outputAmount, setOutputAmount] = useState(savedOutputAmount.current);

    function formatAmount(amount) {
      try {
        if (!amount || amount === "0" || amount === "") {
          return "";
        }

        const parsedAmount = parseFloat(amount);
        if (isNaN(parsedAmount)) {
          return "";
        }

        return parsedAmount.toFixed(8);
      } catch (error) {
        return "";
      }
    }
    useEffect(() => {
      const handle = setInterval(() => {
        if (Number(savedOutputAmount.current) !== Number(outputAmount)) {
          if (Number(savedInputAmount.current) > 0) {
            setOutputAmount(savedOutputAmount.current);
          } else {
            setOutputAmount("");
          }
        }
        if (
          Number(savedInputAmount.current) === 0 ||
          savedInputAmount.current == "0" ||
          savedInputAmount.current == ""
        ) {
          savedOutputAmount.current = "0";
          if (outputAmount !== "") {
            /*         console.log(
              ' Number(savedInputAmount.current) === 0',
              savedInputAmount.current
            ); */
            setOutputAmount("");
          }
        }
      }, 1000);

      return () => clearInterval(handle);
    }, [savedOutputAmount, outputAmount]);

    const [newListing, setNewListing] = useState(false);

    useEffect(() => {
      if (Object.keys(ALL_TOKENS)[3] === buyToken) {
        setNewListing(true);
      } else {
        setNewListing(false);
      }
    }, [buyToken]);

    return (
      <div className="flex-col">
        <div className="token-input-box">
          <div className="token-input-box-top">
            <div className="small-text">Buy</div>
            <div
              className="token-select-box"
              onClick={() => setShowTokenList("buyToken")}
            >
              {newListing && <div className="new-listing">NEW</div>}
              <img
                src={ALL_TOKENS[buyToken]?.logo_uri}
                alt={ALL_TOKENS[buyToken]?.name}
                width={25}
                height={25}
                style={{ borderRadius: "50%" }}
              />{" "}
              <div className="med-text">{ALL_TOKENS[buyToken]?.symbol}</div>
              <DownArrow />
            </div>
          </div>
          <div className="flex-row bt">
            <input
              className="token-input"
              placeholder={"0"}
              value={formatAmount(outputAmount)}
              type="number"
              readOnly
            />
          </div>
          <div className="flex-row">
            <div className="small-text">
              {outputAmount && Number(outputAmount) > 0 ? (
                <DollarValue
                  Token={ALL_TOKENS[buyToken]}
                  isOutputToken={true}
                  onValueChange={(value) => {
                    buyUsdValueRef.current = value;
                  }}
                />
              ) : (
                "-"
              )}
            </div>

            <div className="small-text">
              <div className="max-row">
                <WalletIcon />
                {balanceDisplayFixer(buyTokenDisplayBalance)}{" "}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
  const memoYouReceive = useMemo(() => {
    return (
      <YouReceive
        setShowTokenList={setShowTokenList}
        ALL_TOKENS={ALL_TOKENS}
        buyTokenDisplayBalance={buyTokenDisplayBalance}
      />
    );
  }, [setShowTokenList, ALL_TOKENS, buyTokenDisplayBalance]);
  function SaverInfo({
    swapData,
    savedSlippage,
    savedSlippageValue,
    savedAddedPriority,
  }) {
    const [slippagePercentage, setSlippagePercentage] = useState(
      savedSlippageValue.current || 0.5
    );
    const [minTokensOut, setMinTokensOut] = useState("0");
    const [pairRoute, setPairRoute] = useState("");
    const [gasType, setGasType] = useState("Auto");
    const [gasGwei, setGasGwei] = useState("1");
    const [loading, setLoading] = useState(true);

    useEffect(() => {
      const handle = setInterval(() => {
        const refSlippage = savedSlippageValue.current;
        if (refSlippage !== slippagePercentage) {
          setSlippagePercentage(refSlippage);
        }

        const currentGasType = savedAddedPriority?.current;
        if (currentGasType) {
          let gasTypeLabel = "Auto";
          let gasGweiValue = "0.1";

          if (currentGasType === "auto") {
            gasTypeLabel = "Auto";
            gasGweiValue = "0.1";
          } else if (typeof currentGasType === "number") {
            if (currentGasType === 2) {
              gasTypeLabel = "Average";
              gasGweiValue = "2";
            } else if (currentGasType === 3) {
              gasTypeLabel = "Quick";
              gasGweiValue = "3";
            } else if (currentGasType === 5) {
              gasTypeLabel = "Instant";
              gasGweiValue = "5";
            } else {
              gasTypeLabel = "Custom";
              gasGweiValue = currentGasType.toString();
            }
          }

          setGasType(gasTypeLabel);
          setGasGwei(gasGweiValue);
        }
      }, 500);

      return () => clearInterval(handle);
    }, [slippagePercentage, savedSlippageValue, savedAddedPriority]);

    useEffect(() => {
      if (swapData && slippagePercentage) {
        try {
          let calculatedMinTokensOut = ethers.formatUnits(
            String(swapData.amountOut),
            swapData.decimals
          );

          calculatedMinTokensOut =
            Number(calculatedMinTokensOut) * (1 - slippagePercentage / 100);

          setMinTokensOut(calculatedMinTokensOut);
        } catch (error) {
          console.warn("Error parsing minTokensOut:", error);
        }

        let route = "";
        try {
          if (swapData.isV3Only) {
            route = "V3";
          } else if (swapData.isV2Only) {
            route = "V2";
          } else if (swapData.isV2ToV3) {
            route = "V2 to V3";
          } else if (swapData.isV3ToV2) {
            route = "V3 to V2";
          }
          setPairRoute(route);
        } catch (error) {
          console.warn("Error determining pair route:", error);
        }

        setLoading(false);
      }
    }, [swapData, slippagePercentage]);

    if (loading) {
      return (
        <div className="general-box">
          <div className="saver-text-container">
            <div className="saver-text-left">Route</div>
            <div className="saver-text-right"></div>
          </div>{" "}
          <div className="saver-text-container">
            <div className="saver-text-left">Min Tokens Out</div>
            <div className="saver-text-right"></div>
          </div>{" "}
          <div className="saver-text-container">
            <div className="saver-text-left">Slippage</div>
            <div className="saver-text-right"></div>
          </div>
          <div className="saver-text-container">
            <div className="saver-text-left">Gas</div>
            <div className="saver-text-right"></div>
          </div>
        </div>
      );
    }

    return (
      <div className="general-box">
        <div className="saver-text-container">
          <div className="saver-text-left">Route</div>
          <div
            className={`saver-text-right ${
              pairRoute === "V3"
                ? "route-v3"
                : pairRoute === "V2"
                ? "route-v2"
                : "route-v2-to-v3"
            }`}
          >
            {pairRoute}
          </div>
        </div>
        <div className="saver-text-container">
          <div className="saver-text-left">Min Tokens Out</div>
          <div className="saver-text-right">
            {Number(minTokensOut).toFixed(8)}
          </div>
        </div>
        <div className="saver-text-container">
          <div className="saver-text-left">Slippage</div>
          <div className="saver-text-right">{`${Number(
            slippagePercentage
          ).toFixed(2)}%`}</div>
        </div>
        <div className="saver-text-container">
          <div className="saver-text-left">Gas</div>
          <div className="saver-text-right">
            <span>{`${gasGwei} Gwei`}</span>
            <span className="gas-indicator">
              <span
                className={`gas-indicator-dot gas-${gasType.toLowerCase()}`}
              ></span>
              <span>{`(${gasType})`}</span>
            </span>
          </div>
        </div>
      </div>
    );
  }

  function SaverText() {
    return (
      <div className="saver-container">
        <div className="saver-text-gray">
          Trading on the Pineapple router saves you up to
          <span className="saver-text"> 16% on gas fees</span> compared to the
          Uniswap router. While all DEXs charge a swap fee ranging from 0.25% to
          1.5%, <span className="saver-text">Pineapple charges 0%. </span>
          Pineapple seamlessly aggregates liquidity, ensuring you always get the
          <span className="saver-text"> best price for your trades. </span>
        </div>
      </div>
    );
  }

  const memoQuoteView = useMemo(() => {
    return <QuoteView id="quote-view-component" />;
  }, [sellToken, buyToken]);
  function QuoteView({ id }) {
    const {
      account,
      savedInputAmount,
      updateData,
      signer,
      provider,
      savedSlippage,
      savedSlippageValue,
      savedPriorityGas,
      savedOutputAmount,
      useAutoGas,
      useAutoSlippage,
      savedAddedPriority,
    } = useContext(BlockchainContext);
    const [price, setPrice] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [sellAmount, setSellAmount] = useState(0);
    const [buyAmount, setBuyAmount] = useState("");

    const gasLevelRef = useRef(null);
    const [swapData, setSwapData] = useState(null);

    useEffect(() => {
      const handle = setInterval(() => {
        if (!account) return;
        const inputAmount = Number(savedInputAmount.current);
        const OutputAmount = Number(savedOutputAmount.current);

        const PriorityGas = String(savedPriorityGas.current);

        if (String(savedInputAmount.current) !== String(sellAmount)) {
          console.log("✅ inputAmount amount", inputAmount);
          console.log(
            "✅ savedInputAmount.current amount",
            savedInputAmount.current
          );
          setSellAmount(String(savedInputAmount.current));
          return;
        }

        if (PriorityGas !== gasLevelRef.current) {
          gasLevelRef.current = PriorityGas;
          return;
        }
      }, RATE_LIMIT);
      return () => clearInterval(handle);
    }, [sellAmount]);

    useEffect(() => {
      const handle = setInterval(() => {
        if (!account) return;

        const inputAmount = Number(savedInputAmount.current);

        if (inputAmount === 0 || inputAmount === "0" || inputAmount === "") {
          if (swapData && sellAmount !== 0) {
            setSellAmount(0);
            setSwapData(null);
          }
        }
      }, 500);
      return () => clearInterval(handle);
    }, [sellAmount, swapData]);

    useEffect(() => {
      if (sellAmount > 0 && sellToken && buyToken) {
        if (!account) return;

        console.log("✅ fetchPrice");
        fetchPrice();
      }
    }, [sellAmount, sellToken, buyToken]);

    const blockNumberRef = useRef(0);

    const inSwap = useRef(false);

    // Removed isTransactionInProgress state - using only inSwap.current ref

    useEffect(() => {
      let intervalId;

      const fetchNewBlockNumber = async () => {
        if (!account) return;

        if (inSwap.current === true) {
          console.log("Skipping price update - transaction in progress");
          return;
        }

        try {
          if (sellAmount === 0) {
            return;
          }

          const blockNumber = await fetchBlockNumber(chain_id, authToken);

          if (blockNumberRef.current !== blockNumber) {
            /*             console.log('✅✅✅ New block number:', blockNumber);
            console.log('✅✅✅ Old block number:', blockNumberRef.current);
            console.log('✅✅✅ chain_id:', chain_id);
 */
            if (chain_id !== 1) {
              const isMoreThan3 = blockNumber > blockNumberRef.current + 5;
              if (!isMoreThan3) {
                return;
              }
            }

            blockNumberRef.current = blockNumber;
            console.log("✅✅✅ sellAmount:", sellAmount);

            if (sellAmount !== 0 && sellToken && buyToken) {
              fetchPrice();
            }
          }
        } catch (error) {
          console.warn("Failed to fetch new block number:", error);
        }
      };
      fetchNewBlockNumber();

      intervalId = setInterval(fetchNewBlockNumber, 10000);

      return () => {
        clearInterval(intervalId);
      };
    }, [sellAmount, chain_id]);
    /*
    useEffect(() => {
      let intervalId;

      const fetchNewBlockNumber = async () => {
        if (!providerHTTP) return;

        try {
          const blockNumber = await providerHTTP.getBlockNumber();

          if (blockNumberRef.current !== blockNumber) {
            console.log('✅✅✅ New block number:', blockNumber);
            console.log('✅✅✅ Old block number:', blockNumberRef.current);
            console.log('✅✅✅ chain_id:', chain_id);
            if (chain_id !== 1) {
              const isMoreThan3 = blockNumber > blockNumberRef.current + 5;
              if (!isMoreThan3) {
                return;
              }
            }
            blockNumberRef.current = blockNumber;
            console.log('✅✅✅ sellAmount.current:', sellAmount);
            if (sellAmount !== 0 && sellToken && buyToken) {
              fetchPrice();
            }
          }
        } catch (error) {
          console.warn('Failed to fetch new block number:', error);
        }
      };

      const startPolling = () => {
        intervalId = setInterval(fetchNewBlockNumber, 3000);
      };

      const stopPolling = () => {
        clearInterval(intervalId);
      };

      startPolling();

      return () => {
        stopPolling();
      };
    }, [sellAmount]); */

    async function fetchPrice() {
      if (!ALL_TOKENS[sellToken] || !ALL_TOKENS[buyToken] || !sellAmount) {
        return <div className="swap-button disable">......</div>;
      }

      const v2Only = ALL_TOKENS[buyToken].is_v2 || ALL_TOKENS[sellToken].is_v2;
      console.log("✅✅✅ v2Only:", v2Only);

      if (ALL_TOKENS[buyToken].is_v2 || ALL_TOKENS[sellToken].is_v2) {
        console.log("✅✅✅ is_v2 is true, skipping V3 quotes");
      }

      try {
        const sellTokenDecimals = ALL_TOKENS[sellToken]?.decimals;
        let parsedSellAmount = ethers.parseUnits(sellAmount, sellTokenDecimals);
        console.log("➡️➡️ parsedSellAmount", parsedSellAmount);

        async function getPriceData() {
          try {
            /*            const routerContract = new ethers.Contract(
              uniswapRouterAddress,
              uniswapRouterABI,
              providerHTTP
            );
 */
            const defineSwapType = () => {
              const buyTokenSymbol = ALL_TOKENS[buyToken].symbol;
              const sellTokenSymbol = ALL_TOKENS[sellToken].symbol;
              if (
                sellTokenSymbol === nativeSymbol &&
                buyTokenSymbol !== nativeSymbol
              ) {
                return "ETH/TOKEN";
              } else if (
                sellTokenSymbol !== nativeSymbol &&
                buyTokenSymbol === nativeSymbol
              ) {
                return "TOKEN/ETH";
              } else {
                return "TOKEN/TOKEN";
              }
            };

            const getEthToTokenBestQuote = async () => {
              let v2Quote = null;
              let v3Quote = null;

              try {
                let amountsOutV2;

                try {
                  const apiPath = [wethAddress, ALL_TOKENS[buyToken].address];
                  const response = await fetch(
                    "/api/rpc-call/get-amounts-out",
                    {
                      method: "POST",
                      headers: {
                        "Content-Type": "application/json",
                        Authorization: `Bearer ${authToken}`,
                      },
                      body: JSON.stringify({
                        chain_id,
                        amountIn: parsedSellAmount.toString(),
                        path: apiPath,
                        uniswapRouterAddress,
                      }),
                    }
                  );

                  const data = await response.json();

                  if (response.ok) {
                    amountsOutV2 = data.amounts;
                    v2Quote = amountsOutV2[1];
                    console.log("V2 Quote:", v2Quote);
                  } else {
                    console.warn("Error with V2 quote:", data.error);
                    v2Quote = null;
                  }
                } catch (error) {
                  console.warn("Error fetching V2 quote:", error);
                  v2Quote = null;
                }
              } catch (error) {
                console.warn("Fetching V2 quote failed:", error);
              }

              try {
                if (v2Only) {
                  throw new Error("Manual override V2 only");
                }
                console.log("Attempting to fetch V3 quote");

                v3Quote = await getUniswapQuoteV3(
                  wethAddress,
                  ALL_TOKENS[buyToken].address,
                  parsedSellAmount,
                  chain_id,
                  authToken
                );

                console.log("V3 Quote:", v3Quote);
              } catch (error) {
                console.warn("Fetching V3 quote failed:", error);
              }

              if (v2Quote !== null && v3Quote !== null) {
                const v2QuoteBigInt = BigInt(v2Quote);
                const v3QuoteAmountOutBigInt = BigInt(v3Quote.amountOut);

                console.log(
                  "⚡ v2QuoteBigInt > v3QuoteAmountOutBigInt",
                  v2QuoteBigInt > v3QuoteAmountOutBigInt
                );

                return v2QuoteBigInt > v3QuoteAmountOutBigInt
                  ? {
                      bestQuote: v2QuoteBigInt,
                      isV3Only: false,
                      isV2Only: true,
                    }
                  : {
                      bestQuote: v3QuoteAmountOutBigInt,
                      fee: v3Quote.fee,
                      isV3Only: true,
                      isV2Only: false,
                    };
              } else if (v2Quote !== null) {
                return {
                  bestQuote: BigInt(v2Quote),
                  isV3Only: false,
                  isV2Only: true,
                };
              } else if (v3Quote !== null) {
                return {
                  bestQuote: BigInt(v3Quote.amountOut),
                  isV3Only: true,
                  isV2Only: false,
                  fee: v3Quote.fee,
                };
              } else {
                console.warn("No quotes available for ETH to Token swap.");
                return null;
              }
            };

            const getTokenToEthBestQuote = async () => {
              let v2Quote = null;
              let v3Quote = null;

              try {
                /*                 const amountsOutV2 = await routerContract.getAmountsOut(
                  parsedSellAmount,
                  [ALL_TOKENS[sellToken].address, wethAddress]
                ); */
                const apiPath = [ALL_TOKENS[sellToken].address, wethAddress];
                const amountsOutV2 = await getAmountOutV2(
                  chain_id,
                  parsedSellAmount,
                  apiPath,
                  uniswapRouterAddress,
                  authToken
                );
                v2Quote = amountsOutV2[1];
                console.log("V2 Quote:", v2Quote);
              } catch (error) {
                console.warn("Fetching V2 quote failed:", error);
              }

              try {
                /*                 v3Quote = await getQuoteV3(
                  ALL_TOKENS[sellToken].address,
                  wethAddress,
                  parsedSellAmount,
                  chain_id
                ); */

                if (v2Only) {
                  throw new Error(
                    "Manual override V2 only - token has is_v2 flag"
                  );
                }
                v3Quote = await getUniswapQuoteV3(
                  ALL_TOKENS[sellToken].address,
                  wethAddress,
                  parsedSellAmount,
                  chain_id,
                  authToken
                );
                console.log("V3 Quote:", v3Quote);
              } catch (error) {
                console.warn("Fetching V3 quote failed:", error);
              }

              if (v2Quote !== null && v3Quote !== null) {
                const v2QuoteBigInt = BigInt(v2Quote);
                const v3QuoteAmountOutBigInt = BigInt(v3Quote.amountOut);

                console.log(
                  "⚡ v2QuoteBigInt > v3QuoteAmountOutBigInt",
                  v2QuoteBigInt > v3QuoteAmountOutBigInt
                );

                return v2QuoteBigInt > v3QuoteAmountOutBigInt
                  ? {
                      bestQuote: v2QuoteBigInt,
                      isV3Only: false,
                      isV2Only: true,
                    }
                  : {
                      bestQuote: v3QuoteAmountOutBigInt,
                      isV3Only: true,
                      isV2Only: false,
                      fee: v3Quote.fee,
                    };
              } else if (v2Quote !== null) {
                return {
                  bestQuote: BigInt(v2Quote),
                  isV3Only: false,
                  isV2Only: true,
                };
              } else if (v3Quote !== null) {
                return {
                  bestQuote: BigInt(v3Quote.amountOut),
                  isV3Only: true,
                  isV2Only: false,
                  fee: v3Quote.fee,
                };
              } else {
                console.warn(
                  "No valid quotes were found for Token to ETH swap."
                );
                return null;
              }
            };

            const getTokenToTokenBestQuote = async () => {
              let v2Quote = null;
              let v3Quote = null;
              let v3QuoteFee = null;
              let v3QuoteFeeIn = null;
              let v3QuoteFeeOut = null;
              let v2ToV3Quote = null;
              let v3ToV2Quote = null;

              const pathV2 = [
                ALL_TOKENS[sellToken].address,
                wethAddress,
                ALL_TOKENS[buyToken].address,
              ];
              const pathV3 = {
                tokenIn: ALL_TOKENS[sellToken].address,
                tokenOut: ALL_TOKENS[buyToken].address,
              };

              try {
                /*               const amountsOutV2 = await routerContract.getAmountsOut(
                  parsedSellAmount,
                  pathV2
                ); */
                const amountsOutV2 = await getAmountOutV2(
                  chain_id,
                  parsedSellAmount,
                  pathV2,
                  uniswapRouterAddress,
                  authToken
                );
                v2Quote = amountsOutV2[amountsOutV2.length - 1];
                console.log("V2 Quote:", v2Quote);
              } catch (error) {
                console.warn("Error fetching V2 quote:", error);
              }

              try {
                /*         const directV3Quote = await getQuoteV3(
                  pathV3.tokenIn,
                  pathV3.tokenOut,
                  parsedSellAmount,
                  chain_id
                ); */

                if (
                  v2Only ||
                  ALL_TOKENS[buyToken].force_v2 ||
                  ALL_TOKENS[sellToken].force_v2
                ) {
                  throw new Error(
                    "Manual override V2 only - token has force_v2 or is_v2 flag"
                  );
                }
                const directV3Quote = await getUniswapQuoteV3(
                  String(pathV3.tokenIn),
                  String(pathV3.tokenOut),
                  parsedSellAmount,
                  chain_id,
                  authToken
                );
                v3Quote = directV3Quote.amountOut;
                v3QuoteFee = directV3Quote.fee;
                v3QuoteFeeIn = directV3Quote.feeIn;
                v3QuoteFeeOut = directV3Quote.feeOut;

                console.log("V3 Direct Quote:", directV3Quote);
              } catch (error) {
                console.warn("Error fetching V3 direct quote:", error);
              }

              try {
                /*           const amountsOutV2ToV3 = await routerContract.getAmountsOut(
                  parsedSellAmount,
                  [ALL_TOKENS[sellToken].address, wethAddress]
                ); */
                const pathV2ToV3 = [ALL_TOKENS[sellToken].address, wethAddress];
                const amountsOutV2ToV3 = await getAmountOutV2(
                  chain_id,
                  parsedSellAmount,
                  pathV2ToV3,
                  uniswapRouterAddress,
                  authToken
                );
                /*       const v2ToV3 = await getQuoteV3(
                  wethAddress,
                  ALL_TOKENS[buyToken].address,
                  amountsOutV2ToV3[1],
                  chain_id
                ); */

                if (v2Only) {
                  throw new Error(
                    "Manual override V2 only - token has is_v2 flag"
                  );
                }
                const v2ToV3 = await getUniswapQuoteV3(
                  wethAddress,
                  ALL_TOKENS[buyToken].address,
                  amountsOutV2ToV3[1],
                  chain_id,
                  authToken
                );
                v2ToV3Quote = v2ToV3.amountOut;
                v3QuoteFee = v2ToV3.fee;
                console.log("V2 to V3 Quote:", v2ToV3Quote);
              } catch (error) {
                console.warn("Error fetching V2 to V3 quote:", error);
              }
              try {
                /*            const v3ToV2 = await getQuoteV3(
                  ALL_TOKENS[sellToken].address,
                  wethAddress,
                  parsedSellAmount,
                  chain_id
                ); */

                if (v2Only) {
                  throw new Error(
                    "Manual override V2 only - token has is_v2 flag"
                  );
                }
                const v3ToV2 = await getUniswapQuoteV3(
                  ALL_TOKENS[sellToken].address,
                  wethAddress,
                  parsedSellAmount,
                  chain_id,
                  authToken
                );
                v3QuoteFee = v3ToV2.fee;
                /*                 const amountsOutV3ToV2 = await routerContract.getAmountsOut(
                  v3ToV2.amountOut,
                  [wethAddress, ALL_TOKENS[buyToken].address]
                );
 */
                const pathV3ToV2 = [wethAddress, ALL_TOKENS[buyToken].address];
                const amountsOutV3ToV2 = await getAmountOutV2(
                  chain_id,
                  v3ToV2.amountOut,
                  pathV3ToV2,
                  uniswapRouterAddress,
                  authToken
                );
                v3ToV2Quote = amountsOutV3ToV2[1];
                console.log("V3 to V2 Quote:", v3ToV2Quote);
              } catch (error) {
                console.warn("Error fetching V3 to V2 quote:", error);
              }
              const quotes = [
                v2Quote !== null ? BigInt(v2Quote) : null,
                v3Quote !== null ? BigInt(v3Quote) : null,
                v2ToV3Quote !== null ? BigInt(v2ToV3Quote) : null,
                v3ToV2Quote !== null ? BigInt(v3ToV2Quote) : null,
              ].filter((q) => q !== null);

              if (quotes.length === 0) {
                console.warn(
                  "No valid quotes were found for Token to Token swap."
                );
                return {
                  bestQuote: BigInt(0),
                  isV3Only: false,
                  isV2Only: false,
                  fee: BigInt(0),
                };
              }

              let bestQuote = BigInt(0);

              for (const quote of quotes) {
                if (BigInt(quote) > bestQuote) {
                  console.log(`New best quote found: ${quote}`);
                  bestQuote = BigInt(quote);
                }
              }

              console.log(
                `Overall best quote before any comparison: ${bestQuote}`
              );

              console.log("v2Quote:", v2Quote);
              console.log("v3Quote:", v3Quote);
              const directQuotes = [v2Quote, v3Quote]
                .filter((quote) => quote !== null)
                .map(BigInt);

              let bestDirectQuote = BigInt(0);

              for (const quote of directQuotes) {
                if (quote > bestDirectQuote) {
                  console.log(`New best direct quote found: ${quote}`);
                  bestDirectQuote = quote;
                }
              }

              console.log(
                `Best direct quote for comparison: ${bestDirectQuote}`
              );

              if (
                (v2ToV3Quote !== null &&
                  BigInt(bestQuote) === BigInt(v2ToV3Quote)) ||
                (v3ToV2Quote !== null &&
                  BigInt(bestQuote) === BigInt(v3ToV2Quote) &&
                  BigInt(bestQuote) !== BigInt(0) &&
                  BigInt(bestDirectQuote) !== BigInt(0))
              ) {
                const improvement =
                  ((BigInt(bestQuote) - BigInt(bestDirectQuote)) *
                    BigInt(100)) /
                  BigInt(bestDirectQuote);

                console.log(
                  `Improvement percentage: ${improvement}% from mixed route`
                );

                if (improvement < BigInt(5)) {
                  console.log(
                    "Choosing the best direct quote due to less than 5% improvement by mixed route."
                  );
                  bestQuote = BigInt(bestDirectQuote);
                }
              }

              /*        const isV3Only = [v3Quote].includes(bestQuote);
              const isV2Only = [v2Quote].includes(bestQuote);
              const isV2ToV3 = [v2ToV3Quote].includes(bestQuote);
              const isV3ToV2 = [v3ToV2Quote].includes(bestQuote); */
              v3Quote = v3Quote ? BigInt(v3Quote) : null;
              v2Quote = v2Quote ? BigInt(v2Quote) : null;
              v2ToV3Quote = v2ToV3Quote ? BigInt(v2ToV3Quote) : null;
              v3ToV2Quote = v3ToV2Quote ? BigInt(v3ToV2Quote) : null;
              const isV3Only = v3Quote !== null && bestQuote === v3Quote;
              const isV2Only = v2Quote !== null && bestQuote === v2Quote;
              const isV2ToV3 =
                v2ToV3Quote !== null && bestQuote === v2ToV3Quote;
              const isV3ToV2 =
                v3ToV2Quote !== null && bestQuote === v3ToV2Quote;
              console.log("Best quote:", bestQuote, typeof bestQuote);
              console.log("v2Quote:", v2Quote, typeof v2Quote);
              console.log("v3Quote:", v3Quote, typeof v3Quote);

              const fee = isV3Only ? v3QuoteFee : 0;

              console.log(`Final selected best quote: ${bestQuote}`);
              console.log(
                `Route details - Is V3 only: ${isV3Only}, Is V2 only: ${isV2Only}, Is V2 to V3: ${isV2ToV3}, Is V3 to V2: ${isV3ToV2}`
              );

              return {
                bestQuote: bestQuote,
                isV3Only,
                isV2Only,
                isV2ToV3,
                isV3ToV2,
                v3QuoteFee,
                fee,
                v3QuoteFeeIn,
                v3QuoteFeeOut,
              };
            };
            const swapType = defineSwapType();
            console.log("swapType", swapType);
            let bestQuote;
            switch (swapType) {
              case "ETH/TOKEN":
                bestQuote = await getEthToTokenBestQuote();
                break;
              case "TOKEN/ETH":
                bestQuote = await getTokenToEthBestQuote();
                break;
              case "TOKEN/TOKEN":
                bestQuote = await getTokenToTokenBestQuote();
                break;
              default:
                throw new Error("Invalid swap type");
            }
            const deadline = Math.floor(Date.now() / 1000) + 60 * 20;

            const data = {
              fromTokenAddress: ALL_TOKENS[sellToken].address,
              buyAmount: bestQuote.bestQuote,
              sellTokenAddress: ALL_TOKENS[sellToken].address,
              buyTokenAddress: ALL_TOKENS[buyToken].address,
              amount: parsedSellAmount,
              isV3Only: bestQuote.isV3Only,
              isV2Only: bestQuote.isV2Only,
              isV2ToV3: bestQuote.isV2ToV3,
              isV3ToV2: bestQuote.isV3ToV2,
              swapType: swapType,
              sqrtPriceLimitX96: 0,
              deadline: deadline,
              v3QuoteFeeIn: bestQuote.v3QuoteFeeIn || 0,
              v3QuoteFeeOut: bestQuote.v3QuoteFeeOut || 0,
              fee: bestQuote.fee,
              v3QuoteFee: bestQuote.v3QuoteFee,
            };
            console.log("--- data", data);
            return data;
          } catch (error) {
            console.warn("error", error);
            return null;
          }
        }

        let price;
        if (
          (ALL_TOKENS[sellToken].symbol === nativeSymbol &&
            ALL_TOKENS[buyToken].symbol === "WETH") ||
          (ALL_TOKENS[sellToken].symbol === "WETH" &&
            ALL_TOKENS[buyToken].symbol === nativeSymbol)
        ) {
          let buyTokenIsWETH = ALL_TOKENS[buyToken].symbol === "WETH";
          let sellTokenIsWETH = ALL_TOKENS[sellToken].symbol === "WETH";
          console.log("buyTokenIsWETH", buyTokenIsWETH);
          console.log("sellTokenIsWETH", sellTokenIsWETH);
          console.log("wethAddress", wethAddress);
          console.log("ethAddress", ethAddress);

          price = {
            fromTokenAddress: sellTokenIsWETH ? wethAddress : ethAddress,
            tokenIn: sellTokenIsWETH ? wethAddress : ethAddress,
            toTokenAddress: buyTokenIsWETH ? wethAddress : ethAddress,
            tokenOut: buyTokenIsWETH ? wethAddress : ethAddress,
            decimals: ALL_TOKENS[buyToken].decimals,
            amount: parsedSellAmount,
            amountIn: parsedSellAmount || "0",
            amountOut: parsedSellAmount || "0",
            buyAmount: parsedSellAmount || "0",
            slippage: 0,
            to: account,
            recipient: account,
            fee: 0,
            isV3Only: false,
            isV2Only: false,
            isV2ToV3: false,
            isV3ToV2: false,
            swapType: false,
            sqrtPriceLimitX96: 0,
            deadline: Math.floor(Date.now() / 1000) + 60 * 2,
            feeAddress: feeAddress,
            v3QuoteFeeIn: 0,
            v3QuoteFeeOut: 0,
            v3QuoteFee: 0,
            isWethToEth: sellTokenIsWETH,
            isEthToWeth: buyTokenIsWETH,
          };
          console.log("price", price);
        } else {
          price = await getPriceData();
        }

        let swapDataFormat;

        const slippageValue = savedSlippageValue.current / 100;

        let gasType = "Auto";
        let gasGwei = "2";

        if (savedAddedPriority.current) {
          const priorityType = savedAddedPriority.current;

          if (priorityType === "custom") {
            gasGwei = savedPriorityGas.current || "5";
            gasType = "Custom";
          } else if (priorityType === "auto") {
            gasGwei = "2";
            gasType = "Auto";
          } else if (priorityType === 2) {
            gasGwei = "2";
            gasType = "Average";
          } else if (priorityType === 3) {
            gasGwei = "3";
            gasType = "Quick";
          } else if (priorityType === 5) {
            gasGwei = "5";
            gasType = "Instant";
          } else {
            gasGwei = priorityType.toString();
            gasType = "Custom";
          }
        }

        console.log(
          `Setting gas values in swapData: ${gasGwei} Gwei (${gasType})`
        );

        swapDataFormat = {
          fromTokenAddress: price.sellTokenAddress,
          tokenIn: price.sellTokenAddress,
          toTokenAddress: price.buyTokenAddress,
          tokenOut: price.buyTokenAddress,
          decimals: ALL_TOKENS[buyToken].decimals,
          amount: price.amount || "0",
          amountIn: price.amount || "0",
          amountOut: price.buyAmount || "0",
          slippage: slippageValue,
          to: account,
          recipient: account,
          fee: price.fee,
          isV3Only: price.isV3Only,
          isV2Only: price.isV2Only,
          isV2ToV3: price.isV2ToV3,
          isV3ToV2: price.isV3ToV2,
          swapType: price.swapType,
          sqrtPriceLimitX96: price.sqrtPriceLimitX96,
          deadline: price.deadline,
          feeAddress: feeAddress,
          v3QuoteFeeIn: price.v3QuoteFeeIn,
          v3QuoteFeeOut: price.v3QuoteFeeOut,
          v3QuoteFee: price.v3QuoteFee,

          gasType: gasType,
          gasGwei: gasGwei,
          priorityGas: savedPriorityGas.current || "2",
        };
        console.log("swapDataFormat", swapDataFormat);

        setSwapData(swapDataFormat);

        try {
          let formatBuyAmount = ethers.formatUnits(
            price.buyAmount,
            ALL_TOKENS[buyToken]?.decimals || 18
          );

          const numAmount = Number(formatBuyAmount);
          if (!isNaN(numAmount) && numAmount > 0) {
            const formattedAmount = numAmount.toFixed(8);
            console.log("➡️➡️formatBuyAmount", formattedAmount);

            updateData("savedOutputAmount", formattedAmount);
          } else {
            console.log("Invalid buy amount, setting to 0");
            updateData("savedOutputAmount", "0");
          }
        } catch (error) {
          console.warn("Error formatting buy amount:", error);
          updateData("savedOutputAmount", "0");
        }
      } catch (e) {
        console.log("error", e);
      }
    }

    if (error || loading || !swapData) {
      return (
        <>
          {" "}
          {!account ? (
            <div className="swap-button">Connect Wallet</div>
          ) : (
            <div className="swap-button disable">Enter an amount</div>
          )}
          {/*           {!showAudits && <SaverText />}
           */}{" "}
        </>
      );
    }
    console.log("swapData", swapData);

    function SwapFinal({ swapData }) {
      function ApproveOrSwap({ swapData }) {
        const largeAmount = 11579208923731619542357098500868790n;

        const { account, signer, updateData } = useContext(BlockchainContext);
        const [isApprovalNeeded, setIsApprovalNeeded] = useState(false);
        const [pendingTransaction, setPendingTransaction] = useState(false);

        useEffect(() => {
          const checkAllowance = async () => {
            if (!account) return;

            try {
              if (ALL_TOKENS[sellToken].symbol === nativeSymbol) {
                if (isApprovalNeeded) {
                  setIsApprovalNeeded(false);
                }
                return;
              }
              const tokenContract = new ethers.Contract(
                ALL_TOKENS[sellToken].address,
                erc20Abi,
                signer
              );

              const allowance = await tokenContract.allowance(
                account,
                routerAddress
              );

              const bigIntAllowance = BigInt(allowance);

              const fiftyPercentOfLargeAmount = largeAmount / 2n;
              let newApprovalNeeded =
                bigIntAllowance < fiftyPercentOfLargeAmount;
              const inputAmount = swapData.amount;

              if (bigIntAllowance < inputAmount) {
                newApprovalNeeded = true;
              } else if (bigIntAllowance >= inputAmount) {
                newApprovalNeeded = false;
              }

              setIsApprovalNeeded(newApprovalNeeded);
            } catch (error) {
              console.warn("Failed to fetch allowance:", error);
            }
          };
          checkAllowance();

          const intervalId = setInterval(checkAllowance, 5000);

          return () => clearInterval(intervalId);
        }, [sellToken]);
        async function getGasFees() {
          try {
            const customPriorityGas = savedAddedPriority.current;
            console.log("Custom Priority Gas Setting:", customPriorityGas);

            async function fetchGasPrice(chain_id) {
              try {
                const response = await fetch("/api/rpc-call/get-gas-price", {
                  method: "POST",
                  headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${authToken}`,
                  },
                  body: JSON.stringify({ chain_id }),
                });

                const data = await response.json();

                if (response.ok) {
                  console.log("Gas Price:", data);
                  return data.gasPrice;
                } else {
                  console.warn("Error:", data.error);
                }
              } catch (error) {
                console.warn("Failed to fetch gas price:", error);
              }
            }

            const data = await fetchGasPrice(chain_id);
            console.log("Gas Price Data:", data);
            const estimatedPrices = data.blockPrices[0].estimatedPrices;

            const recommended = estimatedPrices.find(
              (price) => price.confidence === 95
            );

            let maxPriorityFeePerGas;
            let baseFeePerGas;

            let baseFeeValue =
              recommended.baseFeePerGas ||
              recommended.maxFeePerGas - recommended.maxPriorityFeePerGas;

            let baseFeeString = String(baseFeeValue);

            if (baseFeeString.includes(".")) {
              const parts = baseFeeString.split(".");

              if (parts[1].length > 9) {
                parts[1] = parts[1].substring(0, 9);
              }
              baseFeeString = parts[0] + "." + parts[1];
            }

            console.log("Formatted base fee string:", baseFeeString);

            baseFeePerGas = ethers.parseUnits(baseFeeString, "gwei");

            if (customPriorityGas === "custom") {
              const customGasValue = savedPriorityGas.current;
              console.log("Using custom gas value:", customGasValue);

              const safeGasValue = Math.min(Number(customGasValue), 10);
              console.log("Safe gas value (capped at 10 gwei):", safeGasValue);

              const formattedValue = String(safeGasValue);
              maxPriorityFeePerGas = ethers.parseUnits(formattedValue, "gwei");
            } else if (customPriorityGas === "auto") {
              let priorityFeeString = String(recommended.maxPriorityFeePerGas);

              if (priorityFeeString.includes(".")) {
                const parts = priorityFeeString.split(".");

                if (parts[1].length > 9) {
                  parts[1] = parts[1].substring(0, 9);
                }
                priorityFeeString = parts[0] + "." + parts[1];
              }

              console.log("Formatted priority fee string:", priorityFeeString);

              maxPriorityFeePerGas = ethers.parseUnits(
                priorityFeeString,
                "gwei"
              );
            } else {
              maxPriorityFeePerGas = ethers.parseUnits(
                String(customPriorityGas),
                "gwei"
              );
            }

            const maxFeePerGas = baseFeePerGas + maxPriorityFeePerGas;

            console.log("Base Fee Per Gas:", baseFeePerGas.toString());
            console.log(
              "Max Priority Fee Per Gas:",
              maxPriorityFeePerGas.toString()
            );
            console.log("Max Fee Per Gas:", maxFeePerGas.toString());

            return {
              maxPriorityFeePerGas: maxPriorityFeePerGas.toString(),
              maxFeePerGas: maxFeePerGas.toString(),
            };
          } catch (error) {
            console.warn("Failed to getGasFees:", error);

            const defaultPriorityFee = ethers.parseUnits("2", "gwei");
            const defaultMaxFee = ethers.parseUnits("50", "gwei");
            return {
              maxPriorityFeePerGas: defaultPriorityFee.toString(),
              maxFeePerGas: defaultMaxFee.toString(),
            };
          }
        }

        async function getNonEthGasFees() {
          try {
            const customPriorityGas = savedAddedPriority.current;
            console.log(
              "Custom Priority Gas Setting (non-ETH):",
              customPriorityGas
            );

            async function fetchFeeData(chain_id) {
              try {
                const response = await fetch("/api/rpc-call/get-fee-data", {
                  method: "POST",
                  headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${authToken}`,
                  },
                  body: JSON.stringify({ chain_id }),
                });

                const data = await response.json();

                if (response.ok) {
                  console.log("Fee Data:", data.feeData);
                  return data.feeData;
                } else {
                  console.warn("Error:", data.error);
                }
              } catch (error) {
                console.warn("Failed to fetch fee data:", error);
              }
            }

            const feeData = await fetchFeeData(chain_id);

            const gasPrice = BigInt(feeData.gasPrice || 0);

            let maxPriorityFeePerGas;

            if (customPriorityGas === "custom") {
              const customGasValue = savedPriorityGas.current;
              console.log("Using custom gas value (non-ETH):", customGasValue);

              const safeGasValue = Math.min(Number(customGasValue), 5);
              console.log(
                "Safe gas value (non-ETH, capped at 5 gwei):",
                safeGasValue
              );

              maxPriorityFeePerGas = ethers.parseUnits(
                String(safeGasValue),
                "gwei"
              );
            } else if (customPriorityGas === "auto") {
              maxPriorityFeePerGas = BigInt(100000000);
            } else {
              const safeValue = Math.min(Number(customPriorityGas), 5);
              maxPriorityFeePerGas = ethers.parseUnits(
                String(safeValue),
                "gwei"
              );
            }

            const maxFeePerGas = maxPriorityFeePerGas + gasPrice;

            console.log("Gas Price (non-ETH):", gasPrice.toString());
            console.log(
              "Max Priority Fee Per Gas (non-ETH):",
              maxPriorityFeePerGas.toString()
            );
            console.log("Max Fee Per Gas (non-ETH):", maxFeePerGas.toString());

            return {
              gasPrice: maxFeePerGas.toString(),
              maxPriorityFeePerGas: maxPriorityFeePerGas.toString(),
              maxFeePerGas: maxFeePerGas.toString(),
            };
          } catch (error) {
            console.warn("Failed to getNonEthGasFees:", error);

            const defaultPriorityFee = BigInt(100000000);
            const defaultGasPrice = BigInt(5000000000);
            const defaultMaxFee = defaultPriorityFee + defaultGasPrice;

            return {
              gasPrice: defaultMaxFee.toString(),
              maxPriorityFeePerGas: defaultPriorityFee.toString(),
              maxFeePerGas: defaultMaxFee.toString(),
            };
          }
        }

        const handleApprove = async () => {
          try {
            inSwap.current = true;

            const tokenContract = new ethers.Contract(
              ALL_TOKENS[sellToken].address,
              erc20Abi,
              signer
            );
            let requiredAmount = swapData.amount;
            requiredAmount = String(requiredAmount);
            let gasFees;
            if (chain_id === 1) {
              gasFees = await getGasFees();
            } else {
              gasFees = await getNonEthGasFees();
            }
            console.log("Gas Fees:", gasFees);
            const estimatedGas = isBSC
              ? await tokenContract.approve.estimateGas(
                  routerAddress,
                  largeAmount,
                  {
                    gasPrice: gasFees.gasPrice,
                  }
                )
              : await tokenContract.approve.estimateGas(
                  routerAddress,
                  largeAmount,
                  {
                    maxFeePerGas: gasFees.maxFeePerGas,
                    maxPriorityFeePerGas: gasFees.maxPriorityFeePerGas,
                  }
                );
            console.log("Estimated Gas:", estimatedGas.toString());

            const approveTx = isBSC
              ? await tokenContract.approve(routerAddress, largeAmount, {
                  gasLimit: estimatedGas,
                })
              : await tokenContract.approve(routerAddress, largeAmount, {
                  gasLimit: estimatedGas,
                  maxFeePerGas: gasFees.maxFeePerGas,
                  maxPriorityFeePerGas: gasFees.maxPriorityFeePerGas,
                });

            console.log("Transaction Hash:", approveTx.hash);

            const approvalReceipt = await approveTx.wait();
            if (approvalReceipt.status === 1) {
              toast.success("Approval successful");
              setIsApprovalNeeded(false);
            } else {
              setIsApprovalNeeded(true);

              toast.error("Approval failed");
              console.warn("Failed to approve");
              return;
            }
          } catch (error) {
            console.warn("Failed to approve:", error);
          } finally {
            inSwap.current = false;
          }
        };

        const handleSwap = async (retryCount = 0) => {
          try {
            const ethResponse = await fetch("/api/rpc-call/get-balance", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${authToken}`,
              },
              body: JSON.stringify({ chain_id, account }),
            });

            const ethData = await ethResponse.json();
            if (ethResponse.ok) {
              const ethBalance = BigInt(ethData.balance);

              const minGasNeeded = BigInt(***************);

              const ethBalanceFormatted = Number(ethBalance) / 1e18;
              const minGasNeededFormatted = Number(minGasNeeded) / 1e18;

              if (ethBalance < minGasNeeded) {
                toast.error(
                  `Not enough ETH for gas! You have ${ethBalanceFormatted.toFixed(
                    6
                  )} ETH, but need at least ${minGasNeededFormatted.toFixed(
                    6
                  )} ETH for gas.`,
                  { duration: 8000 }
                );
                return;
              }

              console.log(
                `Gas check passed: ${ethBalanceFormatted.toFixed(
                  6
                )} ETH available, minimum needed: ${minGasNeededFormatted.toFixed(
                  6
                )} ETH`
              );
            }

            try {
              const inputAmount = parseFloat(savedInputAmount.current || "0");
              console.log(`Checking balance for input amount: ${inputAmount}`);

              if (sellToken === 0) {
                const ethDecimals = 18;

                const inputAmountInWei = BigInt(
                  Math.floor(inputAmount * Math.pow(10, ethDecimals))
                );

                const gasBuffer = BigInt(200000000000000);
                const totalNeeded = inputAmountInWei + gasBuffer;

                const ethBalanceFormatted = Number(ethBalance) / 1e18;
                const totalNeededFormatted = Number(totalNeeded) / 1e18;

                console.log(
                  `ETH balance check: Have ${ethBalanceFormatted.toFixed(
                    6
                  )} ETH, ` +
                    `need ${inputAmount} ETH + ~0.0002 ETH for gas = ${totalNeededFormatted.toFixed(
                      6
                    )} ETH`
                );

                if (ethBalance < totalNeeded) {
                  toast.error(
                    `Insufficient ETH balance! You have ${ethBalanceFormatted.toFixed(
                      6
                    )} ETH, ` +
                      `but the swap requires ${inputAmount} ETH plus gas.`,
                    { duration: 8000 }
                  );
                  return;
                }

                console.log(
                  `ETH balance check passed: ${ethBalanceFormatted.toFixed(
                    6
                  )} ETH available`
                );
              } else {
                try {
                  const tokenDecimals = ALL_TOKENS[sellToken]?.decimals || 18;
                  const tokenSymbol = ALL_TOKENS[sellToken]?.symbol || "tokens";

                  const tokenBalanceFormatted = await getTokenBalance(
                    sellToken
                  );

                  const tokenBalance = BigInt(
                    Math.floor(
                      parseFloat(tokenBalanceFormatted) *
                        Math.pow(10, tokenDecimals)
                    )
                  );

                  const inputAmountInTokenUnits = BigInt(
                    Math.floor(inputAmount * Math.pow(10, tokenDecimals))
                  );

                  console.log(
                    `Token balance check: Have ${tokenBalanceFormatted} ${tokenSymbol}, need ${inputAmount} ${tokenSymbol}`
                  );

                  if (tokenBalance < inputAmountInTokenUnits) {
                    toast.error(
                      `Insufficient ${tokenSymbol} balance! You have ${tokenBalanceFormatted} ${tokenSymbol}, but the swap requires ${inputAmount} ${tokenSymbol}.`,
                      { duration: 8000 }
                    );
                    return;
                  }

                  console.log(
                    `Token balance check passed: ${tokenBalanceFormatted} ${tokenSymbol} available`
                  );
                } catch (error) {
                  console.warn("Failed to check token balance:", error);
                }
              }
            } catch (tokenError) {
              console.warn("Failed to check token balance:", tokenError);
            }
          } catch (error) {
            console.warn("Failed to check balances:", error);
          }

          try {
            inSwap.current = true;

            if (pendingTxStatus !== "waiting") {
              setPendingTxStatus("waiting");
            }

            /*         const routerContractEstimate = new ethers.Contract(
              routerAddress,
              routerABI,
              providerHTTP
            );
 */
            const routerContract = new ethers.Contract(
              routerAddress,
              routerABI,
              signer
            );
            let transactionResponse;
            let estimatedGas;
            function addGasBuffer(gasLimit) {
              let gas = Number(gasLimit);
              let buffer = gas * 0.2;
              let totalGas = gas + buffer;
              return Math.ceil(totalGas);
            }
            async function preventOverMax(inputAmount) {
              try {
                let inputAmountBN = BigInt(inputAmount.toString());
                let userBalanceBN;
                let result;

                if (ALL_TOKENS[sellToken].symbol === nativeSymbol) {
                  try {
                    const response = await fetch("/api/rpc-call/get-balance", {
                      method: "POST",
                      headers: {
                        "Content-Type": "application/json",
                        Authorization: `Bearer ${authToken}`,
                      },
                      body: JSON.stringify({ chain_id, account }),
                    });

                    const data = await response.json();
                    if (response.ok) {
                      console.log("Balance:", data.balance);
                      userBalanceBN = data.balance;
                    } else {
                      console.warn("Error:", data.error);
                    }
                  } catch (error) {
                    console.warn("Failed to fetch balance:", error);
                  }
                } else {
                  /*                   const tokenContract = new ethers.Contract(
                    ALL_TOKENS[sellToken].address,
                    erc20Abi,
                    providerHTTP
                  );

                  userBalanceBN = BigInt(
                    await tokenContract.balanceOf(account)
                  ); */
                  try {
                    const tokenAddress = ALL_TOKENS[sellToken].address;
                    const response = await fetch(
                      "/api/rpc-call/get-token-balance",
                      {
                        method: "POST",
                        headers: {
                          "Content-Type": "application/json",
                          Authorization: `Bearer ${authToken}`,
                        },
                        body: JSON.stringify({
                          chain_id,
                          account,
                          tokenAddress,
                        }),
                      }
                    );

                    const data = await response.json();
                    if (response.ok) {
                      console.log("Token Balance:", data.tokenBalance);
                      userBalanceBN = data.tokenBalance;
                    } else {
                      console.warn("Error:", data.error);
                    }
                  } catch (error) {
                    console.warn("Failed to fetch token balance:", error);
                  }
                }
                userBalanceBN = BigInt(userBalanceBN);

                if (inputAmountBN > userBalanceBN) {
                  result = userBalanceBN;
                } else {
                  result = inputAmountBN;
                }
                if (retryCount >= 1) {
                  let reductionPercentage = BigInt(10 * retryCount);
                  result =
                    (result * (BigInt(100) - reductionPercentage)) /
                    BigInt(100);
                }

                console.log("result", result);
                return result.toString();
              } catch (error) {
                console.warn("Failed to preventOverMax:", error);
              }
            }

            /*    async function getGasFees() {
  try {
    console.log(savedAddedPriority);
    let gasLevel = gasLevelRef.current;
    const feeData = await provider.getFeeData();


    const baseFeePerGas = feeData.lastBaseFeePerGas;
    const maxPriorityFeePerGas = feeData.maxPriorityFeePerGas;

    console.log('baseFeePerGas', baseFeePerGas.toString());
    console.log('maxPriorityFeePerGas', maxPriorityFeePerGas.toString());

    let priorityFeePerGas = maxPriorityFeePerGas;

    if (savedAddedPriority.current > 0) {
      priorityFeePerGas = ethers.parseUnits(
        String(savedAddedPriority.current),
        'gwei'
      );
      console.log('User-set priorityFeePerGas', priorityFeePerGas.toString());
    }

    console.log('Final priorityFeePerGas', priorityFeePerGas.toString());

    return {
      priorityFeePerGas: priorityFeePerGas.toString(),
    };
  } catch (error) {
    console.warn('Failed to getGasFees:', error);
  }
} */

            let gasFees;
            if (chain_id === 1) {
              gasFees = await getGasFees();
            } else {
              gasFees = await getNonEthGasFees();
            }

            function reduceAmountOut(amountOut) {
              try {
                let minAmountOut = BigInt(amountOut);

                try {
                  let slippageValue = savedSlippageValue.current;

                  console.log(slippageValue, " slippageValue");

                  let slippageInBasisPoints;

                  if (slippageValue < 1) {
                    slippageInBasisPoints = BigInt(
                      Math.floor(slippageValue * 100)
                    );
                    console.log(
                      slippageInBasisPoints.toString(),
                      " basis points (< 1%)"
                    );
                  } else {
                    slippageInBasisPoints = BigInt(Math.floor(slippageValue));
                    console.log(
                      slippageInBasisPoints.toString(),
                      "% slippage (>= 1%)"
                    );
                  }

                  let slippageAmount;

                  if (slippageValue < 1) {
                    slippageAmount =
                      (minAmountOut * slippageInBasisPoints) / BigInt(10000);
                  } else {
                    slippageAmount =
                      (minAmountOut * slippageInBasisPoints) / BigInt(100);
                  }

                  console.log(slippageAmount.toString(), " slippageAmount");

                  minAmountOut = minAmountOut - slippageAmount;

                  if (minAmountOut < BigInt(0)) {
                    minAmountOut = BigInt(0);
                  }

                  if (retryCount >= 1) {
                    let reductionPercentage = BigInt(10 * retryCount);
                    minAmountOut =
                      (minAmountOut * (BigInt(100) - reductionPercentage)) /
                      BigInt(100);
                  }

                  console.log("minAmountOut", minAmountOut);
                } catch (e) {
                  console.warn("error calculating minAmountOut:", e);

                  minAmountOut = (minAmountOut * BigInt(50)) / BigInt(100);
                }
                return minAmountOut;
              } catch (error) {
                console.warn("Fatal error in reduceAmountOut:", error);

                return BigInt(0);
              }
            }
            async function depositEth() {
              const wethContract = new ethers.Contract(
                wethAddress,
                wethABI,
                signer
              );
              let estimatedGas = await wethContract.deposit.estimateGas({
                value: swapData.amount,
              });
              let gasFees = await getGasFees();
              estimatedGas = addGasBuffer(estimatedGas);
              transactionResponse = await wethContract.deposit({
                value: swapData.amount,
                gasLimit: estimatedGas,
                ...(isBSC
                  ? { gasPrice: gasFees.gasPrice }
                  : {
                      maxPriorityFeePerGas: gasFees.maxPriorityFeePerGas,
                      maxFeePerGas: gasFees.maxFeePerGas,
                    }),
              });

              return;
            }
            async function withdrawEth() {
              let gasFees = await getGasFees();
              const wethContract = new ethers.Contract(
                wethAddress,
                wethABI,
                signer
              );
              let estimatedGas = await wethContract.withdraw.estimateGas(
                swapData.amount
              );
              estimatedGas = addGasBuffer(estimatedGas);
              transactionResponse = await wethContract.withdraw(
                swapData.amount,
                {
                  gasLimit: estimatedGas,
                  ...(isBSC
                    ? { gasPrice: gasFees.gasPrice }
                    : {
                        maxPriorityFeePerGas: gasFees.maxPriorityFeePerGas,
                        maxFeePerGas: gasFees.maxFeePerGas,
                      }),
                }
              );
              return;
            }

            if (
              ALL_TOKENS[sellToken].symbol === nativeSymbol &&
              ALL_TOKENS[buyToken].symbol === "WETH"
            ) {
              await depositEth();
            } else if (
              ALL_TOKENS[sellToken].symbol === "WETH" &&
              ALL_TOKENS[buyToken].symbol === nativeSymbol
            ) {
              await withdrawEth();
            } else {
              if (swapData.isV3Only === true) {
                let safeInputAmount = await preventOverMax(swapData.amountIn);
                if (swapData.swapType === "TOKEN/ETH") {
                  console.log("swapData", swapData);
                  estimatedGas =
                    await routerContract.swapTokenToEthV3.estimateGas(
                      swapData.tokenIn,
                      swapData.tokenOut,
                      swapData.fee,
                      swapData.recipient,
                      safeInputAmount,
                      reduceAmountOut(swapData.amountOut),
                      {
                        ...(isBSC
                          ? { gasPrice: gasFees.gasPrice }
                          : {
                              maxPriorityFeePerGas:
                                gasFees.maxPriorityFeePerGas,
                              maxFeePerGas: gasFees.maxFeePerGas,
                            }),
                      }
                    );
                  console.log("estimatedGas", estimatedGas);
                  transactionResponse = await routerContract.swapTokenToEthV3(
                    swapData.tokenIn,
                    swapData.tokenOut,
                    swapData.fee,
                    swapData.recipient,
                    safeInputAmount,
                    reduceAmountOut(swapData.amountOut),

                    {
                      ...(isBSC
                        ? { gasPrice: gasFees.gasPrice }
                        : {
                            maxPriorityFeePerGas: gasFees.maxPriorityFeePerGas,
                            maxFeePerGas: gasFees.maxFeePerGas,
                          }),

                      gasLimit: addGasBuffer(estimatedGas),
                    }
                  );
                  /*              const transaction = {
                    to: routerAddress,
                    data: Irouter.encodeFunctionData('swapTokenToEthV3', [
                      swapData.tokenIn,
                      swapData.tokenOut,
                      BigInt(swapData.fee),
                      swapData.recipient,
                      BigInt(safeInputAmount),
                      BigInt(reduceAmountOut(swapData.amountOut)),
                    ]),
                    gasLimit: BigInt(addGasBuffer(estimatedGas)),
                    maxPriorityFeePerGas: BigInt(gasFees.maxPriorityFeePerGas),
                    maxFeePerGas: BigInt(gasFees.maxFeePerGas),
                    value: BigInt(0),
                  };

                  const transactionHash = ethers.keccak256(transaction.data);
                  console.log('transactionHash', transactionHash);

                  transactionResponse = await signer.signMessage(
                    ethers.getBytes(transactionHash)
                  );
                  console.log('transactionResponse', transactionResponse);
 */
                  /*            */
                } else if (swapData.swapType === "ETH/TOKEN") {
                  estimatedGas =
                    await routerContract.swapEthToTokenV3.estimateGas(
                      swapData.tokenIn,
                      swapData.tokenOut,
                      swapData.fee,
                      swapData.recipient,
                      reduceAmountOut(swapData.amountOut),
                      {
                        ...(isBSC
                          ? { gasPrice: gasFees.gasPrice }
                          : {
                              maxPriorityFeePerGas:
                                gasFees.maxPriorityFeePerGas,
                              maxFeePerGas: gasFees.maxFeePerGas,
                            }),
                        value: safeInputAmount,
                      }
                    );
                  /*                 const transaction = {
                    to: routerAddress,
                    data: Irouter.encodeFunctionData('swapEthToTokenV3', [
                      swapData.tokenIn,
                      swapData.tokenOut,
                      BigInt(swapData.fee),
                      swapData.recipient,
                      BigInt(reduceAmountOut(swapData.amountOut)),
                    ]),
                    gasLimit: BigInt(addGasBuffer(estimatedGas)),
                    maxPriorityFeePerGas: BigInt(gasFees.maxPriorityFeePerGas),
                    maxFeePerGas: BigInt(gasFees.maxFeePerGas),
                    value: BigInt(safeInputAmount),
                  };
                  transactionResponse = await signer.signTransaction(
                    transaction
                  ); */

                  transactionResponse = await routerContract.swapEthToTokenV3(
                    swapData.tokenIn,
                    swapData.tokenOut,
                    swapData.fee,
                    swapData.recipient,
                    reduceAmountOut(swapData.amountOut),
                    {
                      ...(isBSC
                        ? { gasPrice: gasFees.gasPrice }
                        : {
                            maxPriorityFeePerGas: gasFees.maxPriorityFeePerGas,
                            maxFeePerGas: gasFees.maxFeePerGas,
                          }),
                      value: safeInputAmount,
                      gasLimit: addGasBuffer(estimatedGas),
                    }
                  );
                } else if (swapData.swapType === "TOKEN/TOKEN") {
                  estimatedGas =
                    await routerContract.swapTokenToTokenV3.estimateGas(
                      swapData.tokenIn,
                      swapData.tokenOut,
                      swapData.v3QuoteFeeIn,
                      swapData.v3QuoteFeeOut,
                      swapData.recipient,
                      safeInputAmount,
                      reduceAmountOut(swapData.amountOut),
                      {
                        ...(isBSC
                          ? { gasPrice: gasFees.gasPrice }
                          : {
                              maxPriorityFeePerGas:
                                gasFees.maxPriorityFeePerGas,
                              maxFeePerGas: gasFees.maxFeePerGas,
                            }),
                      }
                    );
                  console.log("estimatedGas", estimatedGas);

                  transactionResponse = await routerContract.swapTokenToTokenV3(
                    swapData.tokenIn,
                    swapData.tokenOut,
                    swapData.v3QuoteFeeIn,
                    swapData.v3QuoteFeeOut,
                    swapData.recipient,
                    safeInputAmount,
                    reduceAmountOut(swapData.amountOut),
                    {
                      ...(isBSC
                        ? { gasPrice: gasFees.gasPrice }
                        : {
                            maxPriorityFeePerGas: gasFees.maxPriorityFeePerGas,
                            maxFeePerGas: gasFees.maxFeePerGas,
                          }),
                    }
                  );
                }
              } else if (swapData.isV2Only === true) {
                if (swapData.swapType === "ETH/TOKEN") {
                  let safeInputAmount = await preventOverMax(swapData.amount);
                  console.log("swapData", swapData);
                  estimatedGas =
                    await routerContract.swapExactETHForTokensSupportingFeeOnTransferTokens.estimateGas(
                      reduceAmountOut(swapData.amountOut),
                      [wethAddress, swapData.toTokenAddress],
                      swapData.to,
                      swapData.deadline,
                      {
                        ...(isBSC
                          ? { gasPrice: gasFees.gasPrice }
                          : {
                              maxPriorityFeePerGas:
                                gasFees.maxPriorityFeePerGas,
                              maxFeePerGas: gasFees.maxFeePerGas,
                            }),
                        value: safeInputAmount,
                      }
                    );
                  transactionResponse =
                    await routerContract.swapExactETHForTokensSupportingFeeOnTransferTokens(
                      reduceAmountOut(swapData.amountOut),
                      [wethAddress, swapData.toTokenAddress],
                      swapData.to,
                      swapData.deadline,
                      {
                        ...(isBSC
                          ? { gasPrice: gasFees.gasPrice }
                          : {
                              maxPriorityFeePerGas:
                                gasFees.maxPriorityFeePerGas,
                              maxFeePerGas: gasFees.maxFeePerGas,
                            }),
                        gasLimit: addGasBuffer(estimatedGas),
                        value: safeInputAmount,
                      }
                    );
                } else if (swapData.swapType === "TOKEN/ETH") {
                  let safeInputAmount = await preventOverMax(swapData.amountIn);
                  estimatedGas =
                    await routerContract.swapExactTokensForETHSupportingFeeOnTransferTokens.estimateGas(
                      safeInputAmount,
                      reduceAmountOut(swapData.amountOut),
                      [swapData.fromTokenAddress, wethAddress],
                      swapData.to,
                      swapData.deadline,
                      {
                        ...(isBSC
                          ? { gasPrice: gasFees.gasPrice }
                          : {
                              maxPriorityFeePerGas:
                                gasFees.maxPriorityFeePerGas,
                              maxFeePerGas: gasFees.maxFeePerGas,
                            }),
                        from: account,
                      }
                    );
                  transactionResponse =
                    await routerContract.swapExactTokensForETHSupportingFeeOnTransferTokens(
                      safeInputAmount,
                      reduceAmountOut(swapData.amountOut),
                      [swapData.fromTokenAddress, wethAddress],
                      swapData.to,
                      swapData.deadline,
                      {
                        ...(isBSC
                          ? { gasPrice: gasFees.gasPrice }
                          : {
                              maxPriorityFeePerGas:
                                gasFees.maxPriorityFeePerGas,
                              maxFeePerGas: gasFees.maxFeePerGas,
                            }),
                        gasLimit: addGasBuffer(estimatedGas),
                      }
                    );
                } else if (swapData.swapType === "TOKEN/TOKEN") {
                  let safeInputAmount = await preventOverMax(swapData.amountIn);
                  estimatedGas =
                    await routerContract.swapExactTokensForTokensSupportingFeeOnTransferTokens.estimateGas(
                      safeInputAmount,
                      reduceAmountOut(swapData.amountOut),
                      [
                        swapData.fromTokenAddress,
                        weth,
                        swapData.toTokenAddress,
                      ],
                      swapData.to,
                      swapData.deadline,
                      {
                        ...(isBSC
                          ? { gasPrice: gasFees.gasPrice }
                          : {
                              maxPriorityFeePerGas:
                                gasFees.maxPriorityFeePerGas,
                              maxFeePerGas: gasFees.maxFeePerGas,
                            }),
                      }
                    );
                  transactionResponse =
                    await routerContract.swapExactTokensForTokensSupportingFeeOnTransferTokens(
                      safeInputAmount,
                      reduceAmountOut(swapData.amountOut),
                      [
                        swapData.fromTokenAddress,
                        weth,
                        swapData.toTokenAddress,
                      ],
                      swapData.to,
                      swapData.deadline,
                      {
                        ...(isBSC
                          ? { gasPrice: gasFees.gasPrice }
                          : {
                              maxPriorityFeePerGas:
                                gasFees.maxPriorityFeePerGas,
                              maxFeePerGas: gasFees.maxFeePerGas,
                            }),
                        gasLimit: addGasBuffer(estimatedGas),
                      }
                    );
                }
              } else if (swapData.isV2ToV3 === true) {
                console.log("swapData", swapData);
                let safeInputAmount = await preventOverMax(swapData.amountIn);
                estimatedGas =
                  await routerContract.swapV2TokenToV3Token.estimateGas(
                    swapData.tokenIn,
                    swapData.tokenOut,
                    swapData.v3QuoteFee,
                    swapData.recipient,
                    safeInputAmount,
                    reduceAmountOut(swapData.amountOut),
                    swapData.deadline,
                    {
                      ...(isBSC
                        ? { gasPrice: gasFees.gasPrice }
                        : {
                            maxPriorityFeePerGas: gasFees.maxPriorityFeePerGas,
                            maxFeePerGas: gasFees.maxFeePerGas,
                          }),
                    }
                  );
                transactionResponse = await routerContract.swapV2TokenToV3Token(
                  swapData.tokenIn,
                  swapData.tokenOut,
                  swapData.v3QuoteFee,
                  swapData.recipient,
                  safeInputAmount,
                  reduceAmountOut(swapData.amountOut),
                  swapData.deadline,
                  {
                    ...(isBSC
                      ? { gasPrice: gasFees.gasPrice }
                      : {
                          maxPriorityFeePerGas: gasFees.maxPriorityFeePerGas,
                          maxFeePerGas: gasFees.maxFeePerGas,
                        }),
                    gasLimit: addGasBuffer(estimatedGas),
                  }
                );
              } else if (swapData.isV3ToV2 === true) {
                console.log("swapData", swapData);
                let safeInputAmount = await preventOverMax(swapData.amountIn);
                estimatedGas =
                  await routerContract.swapV3TokenToV2Token.estimateGas(
                    swapData.tokenIn,
                    swapData.tokenOut,
                    swapData.v3QuoteFee,
                    swapData.recipient,
                    safeInputAmount,
                    reduceAmountOut(swapData.amountOut),
                    swapData.deadline,
                    {
                      ...(isBSC
                        ? { gasPrice: gasFees.gasPrice }
                        : {
                            maxPriorityFeePerGas: gasFees.maxPriorityFeePerGas,
                            maxFeePerGas: gasFees.maxFeePerGas,
                          }),
                    }
                  );
                transactionResponse = await routerContract.swapV3TokenToV2Token(
                  swapData.tokenIn,
                  swapData.tokenOut,
                  swapData.v3QuoteFee,
                  swapData.recipient,
                  safeInputAmount,
                  reduceAmountOut(swapData.amountOut),
                  swapData.deadline,
                  {
                    ...(isBSC
                      ? { gasPrice: gasFees.gasPrice }
                      : {
                          maxPriorityFeePerGas: gasFees.maxPriorityFeePerGas,
                          maxFeePerGas: gasFees.maxFeePerGas,
                        }),
                    gasLimit: addGasBuffer(estimatedGas),
                  }
                );
              }
            }
            console.log("transactionResponse", transactionResponse);

            setPendingTransaction(transactionResponse);
            setPendingTxStatus("pending");

            const sendTransaction = await transactionResponse.wait();
            /*      const sendTransaction = await providerHTTP.send(
              'eth_sendRawTransaction',
              [transactionResponse]
            ); */
            console.log("sendTransaction", sendTransaction);

            async function delay(ms) {
              return new Promise((resolve) => setTimeout(resolve, ms));
            }

            await delay(3000);
            if (sendTransaction.status === 1) {
              setPendingTxStatus("confirmed");
              console.log("sendTransaction", sendTransaction);
              const fromAmount = savedInputAmount.current;
              const fromSymbol = ALL_TOKENS[sellToken]?.symbol || "tokens";
              const toAmount =
                Number(swapData.amountOut) /
                Math.pow(10, ALL_TOKENS[buyToken]?.decimals || 18);
              const toSymbol = ALL_TOKENS[buyToken]?.symbol || "tokens";

              try {
                if (swapData && typeof trackSwapEvent === "function") {
                  const sellTokenSymbol = ALL_TOKENS[sellToken].symbol;
                  const buyTokenSymbol = ALL_TOKENS[buyToken].symbol;

                  const swapDetails = {
                    walletId: account,
                    user_dapp_connected_wallet: account,
                    chainId: chain_id.toString(),
                    timestamp: new Date().toISOString(),
                    txHash: sendTransaction.hash,

                    tokenPair: `${sellTokenSymbol}-${buyTokenSymbol}`,

                    fromTokenId: ALL_TOKENS[sellToken].address,
                    fromAmount: savedInputAmount.current,

                    toTokenId: ALL_TOKENS[buyToken].address,
                    toAmount: savedOutputAmount.current,
                  };

                  console.log(
                    "Tracking successful swap event with Cere:",
                    JSON.stringify(swapDetails, null, 2)
                  );
                  await trackSwapEvent(swapDetails);
                  console.log(
                    "Successfully tracked swap event with Cere after confirmation"
                  );
                }
              } catch (error) {
                console.warn("Failed to track swap event with Cere:", error);
              }

              setTrigger(trigger + 1);
              updateData("savedInputAmount", "");
              updateData("savedOutputAmount", "");
            }
            if (sendTransaction.status === 0) {
              setPendingTxStatus("failed");
              setPendingTransaction(false);
            }
          } catch (error) {
            console.warn("Failed to swap:", error);

            console.warn("Swap error details:", {
              code: error.code,
              message: error.message,
              data: error.data,
              reason: error.reason,
            });

            setPendingTxStatus("failed");

            if (
              error.code === -32000 &&
              error.message &&
              error.message.includes("insufficient funds for gas")
            ) {
              toast.error(
                "Not enough ETH for gas! Please add more ETH to your wallet to cover transaction fees.",
                { duration: 6000 }
              );
            } else if (
              error.message?.includes("insufficient funds") ||
              error.code === "INSUFFICIENT_FUNDS"
            ) {
              const tokenSymbol = ALL_TOKENS[sellToken]?.symbol || "tokens";

              if (sellToken !== 0) {
                toast.error(
                  `Insufficient ${tokenSymbol} balance! Please check your wallet and make sure you have enough ${tokenSymbol} for this swap.`,
                  { duration: 6000 }
                );
              } else {
                toast.error(
                  "Insufficient ETH balance! Please check your wallet and make sure you have enough ETH for this transaction.",
                  { duration: 6000 }
                );
              }
            } else if (
              (error.code === -32000 &&
                error.message?.includes("failed with")) ||
              error.message?.includes("gas required exceeds") ||
              error.message?.includes("gas limit")
            ) {
              toast.error(
                "Gas estimation failed! The transaction might be too complex or the network is congested. Try again later.",
                { duration: 6000 }
              );
            } else if (error.message?.includes("slippage")) {
              toast.error(
                "Price moved too much! Try increasing your slippage tolerance in settings.",
                { duration: 6000 }
              );
            } else if (error.message?.includes("TF")) {
              console.log("TF error detected - price impact too high");

              if (retryCount < 3) {
                retryCount++;

                setTimeout(() => {
                  handleSwap(retryCount);
                }, 1000);
                return;
              } else {
              }
            } else if (error.message?.includes("revert")) {
              toast.error(
                "Transaction reverted! This could be due to price movement or contract restrictions. Try increasing slippage.",
                { duration: 6000 }
              );
            } else if (error.message?.includes("user rejected")) {
            } else {
              console.warn("Unhandled swap error:", error);
              toast.error(
                "Swap failed! Please try again with higher slippage or contact support if the issue persists.",
                { duration: 6000 }
              );
            }
          } finally {
            inSwap.current = false;

            console.log(
              "Swap execution completed, final status:",
              pendingTxStatus
            );
          }
        };

        // Using main component state for transaction flow

        // Use inSwap.current ref to prevent re-renders and keep module open
        useEffect(() => {
          if (showTransactionFlow) {
            inSwap.current = true;
          }

          return () => {
            if (!showTransactionFlow) {
              inSwap.current = false;
            }
          };
        }, [showTransactionFlow]);

        // Transaction flow handlers moved to main component level

        return (
          <>
            {account == null ? (
              <div className="swap-button">Connect Wallet</div>
            ) : (
              <>
                {isApprovalNeeded ? (
                  <div
                    className="swap-button"
                    onClick={() => {
                      handleApprove();
                    }}
                  >
                    Approve
                  </div>
                ) : (
                  <>
                    {" "}
                    <div
                      className="swap-button"
                      onClick={() => {
                        console.log(
                          "Opening transaction flow for confirmation"
                        );
                        setShowTransactionFlow(true);
                      }}
                    >
                      Swap
                    </div>
                  </>
                )}
              </>
            )}
          </>
        );
      }

      return swapData ? <ApproveOrSwap swapData={swapData} /> : null;
    }
    return <SwapFinal swapData={swapData} />;
  }

  async function handleContractImport(value) {
    if (value.length !== 42) {
      return;
    }

    try {
      async function fetchTokenData(chain_id, tokenAddress) {
        try {
          const response = await fetch("/api/rpc-call/get-token-data", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${authToken}`,
            },
            body: JSON.stringify({ chain_id, tokenAddress }),
          });

          const data = await response.json();

          if (response.ok) {
            const { chain_id, name, symbol, address, decimals, logo_uri } =
              data;

            console.log("Token Data:", {
              chain_id,
              name,
              symbol,
              address,
              decimals,
              logo_uri,
            });

            const newToken = {
              chain_id,
              name,
              symbol,
              address,
              decimals,
              logo_uri,
              id: Object.keys(ALL_TOKENS).length + 1,
            };

            console.log("New Token:", newToken);
            return newToken;
          } else {
            console.warn("Error:", data.error);
          }
        } catch (error) {
          console.warn("Failed to fetch token data:", error);
        }
      }

      const newToken = await fetchTokenData(chain_id, value);

      if (newToken) {
        const existingToken = Object.values(ALL_TOKENS).find(
          (token) =>
            token.symbol === newToken.symbol ||
            token.address === newToken.address ||
            token.name === newToken.name
        );

        if (!existingToken) {
          let customTokens =
            JSON.parse(localStorage.getItem("customTokens")) || {};
          const customTokenLength = Object.keys(customTokens).length;
          customTokens[customTokenLength] = newToken;
          localStorage.setItem("customTokens", JSON.stringify(customTokens));

          const alltokenslength = Object.keys(ALL_TOKENS).length;
          console.log("alltokenslength", alltokenslength);
          ALL_TOKENS[alltokenslength + 1] = newToken;
          console.log(ALL_TOKENS[alltokenslength + 1]);
          setBuyToken(ALL_TOKENS[alltokenslength + 1].id);
          toast.success(`${newToken.symbol} imported successfully`);

          if (showTokenList !== false) {
            setShowTokenList(false);
          }
        } else {
          console.log(existingToken);
          setBuyToken(ALL_TOKENS[existingToken.id].id);
          if (showTokenList !== false) {
            setShowTokenList(false);
          }

          console.warn(
            `Token ${newToken.symbol} already exists in ALL_TOKENS.`
          );
        }
      }
    } catch (error) {
      console.warn("Failed to import token:", error);
      toast.error("Failed to import token");
    }
  }

  let chartTokenAddress;
  try {
    if (ALL_TOKENS[buyToken].symbol === nativeSymbol) {
      chartTokenAddress = ALL_TOKENS[sellToken].address;
    } else {
      chartTokenAddress = ALL_TOKENS[buyToken].address;
    }
  } catch (error) {
    console.warn(error);
  }

  /*   const memoAudits = useMemo(
    () => (
      <Audit
        contractAddress={chartTokenAddress}
        authToken={authToken}
        chain_id={chain_id}
      />
    ),
    [chartTokenAddress, showAudits]
  ); */
  const memoCharts = useMemo(
    () => (
      <Iframe
        chain_id={chain_id}
        buyToken={chartTokenAddress}
        subTab={"Dexscreener"}
      />
    ),
    [chartTokenAddress]
  );

  /*   const memoBlockTimer = useMemo(() => {
    if (chain_id !== 1) return null;
    return <BlockTimer provider={providerHTTP} chain_id={chain_id} />;
  }, [chain_id]); */

  useEffect(() => {
    const handleResize = () => {
      const mainContainer = document.querySelector(".main-container");
      if (!mainContainer) return;

      if (isMobile === false) {
        const paddingTop = showChart === false ? "18vh" : "12vh";
        mainContainer.style.paddingTop = paddingTop;
      } else {
        mainContainer.style.paddingTop = "0vh";
      }
    };

    handleResize();

    window.addEventListener("resize", handleResize);

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [showChart, isMobile, isJuicedMode]);

  const swapContainer = useRef(null);

  useEffect(() => {
    if (swapContainer.current) {
      swapContainer.current.style.removeProperty("transform");

      swapContainer.current.style.transition = "none";

      swapContainer.current.style.opacity = "1";
    }
  }, []);

  useEffect(() => {
    if (swapContainer.current) {
      if (showChart === true) {
      } else {
        swapContainer.current.style.maxWidth = "";
      }
    }
  }, [showChart]);

  const memoSwapSettings = useMemo(
    () => (
      <SwapSettings
        setShowChartState={setShowChartState}
        showChart={showChart}
        setShowAudits={setShowAudits}
        showAudits={showAudits}
      />
    ),
    [showChart]
  );

  return (
    <div className="main-container" style={{ transition: "none" }}>
      {/*       <AdComponent handleAdChart={handleAdChart} />
       */}{" "}
      {showTokenList === "sellToken" && (
        <TokenList
          handleBuyTokenChange={handleBuyTokenChange}
          handleSellTokenChange={handleSellTokenChange}
          type="sellToken"
          handleShowTokenList={handleShowTokenList}
          key={`sellToken-${chain_id}`}
          buyToken={buyToken}
          sellToken={sellToken}
          handleContractImport={handleContractImport}
        />
      )}
      {showTokenList === "buyToken" && (
        <TokenList
          handleBuyTokenChange={handleBuyTokenChange}
          handleSellTokenChange={handleSellTokenChange}
          type="buyToken"
          handleShowTokenList={handleShowTokenList}
          key={`buyToken-${chain_id}`}
          buyToken={buyToken}
          sellToken={sellToken}
          handleContractImport={handleContractImport}
        />
      )}
      <div
        className="swap-container"
        ref={swapContainer}
        style={{
          transform: "none",
          transition: "none",
          animation: "none",
        }}
      >
        {" "}
        {memoSwapSettings}
        {/*         {isJuicedMode && <JuicedModeSettings />}
         */}{" "}
        <div className="tokens-select-container">
          <YouPay
            setShowTokenList={handleShowTokenList}
            ALL_TOKENS={ALL_TOKENS}
            sellTokenDisplayBalance={sellTokenDisplayBalance}
          />
          <div className="swap-tokens" onClick={swapTokens}>
            <SwitchCurrenciesButton />
          </div>
          <YouReceive
            setShowTokenList={handleShowTokenList}
            ALL_TOKENS={ALL_TOKENS}
            buyTokenDisplayBalance={buyTokenDisplayBalance}
          />
        </div>{" "}
        {/*           {memoBlockTimer}
         */}{" "}
        <QuoteView /> {isETH && showAudits}
      </div>
      <div
        className="mid-section"
        style={{
          opacity: showChart ? 1 : 0,
          top: showChart ? "0" : "-100%",
          left: showChart ? "0" : "-100%",
          position: showChart ? "relative" : "fixed",
          pointerEvents: showChart ? "auto" : "none",
          willChange: "opacity, transform",
        }}
      >
        {memoCharts}
      </div>
      {/* Transaction Review Modal */}
      {showTransactionFlow && (
        <TransactionReview
          isOpen={showTransactionFlow}
          onClose={() => {
            console.log("Closing transaction flow");
            setShowTransactionFlow(false);
            setPendingTxStatus(null);
          }}
          fromToken={
            ALL_TOKENS[sellToken] || {
              symbol: "ETH",
              logoURI:
                "https://tokens.1inch.io/******************************************.png",
            }
          }
          toToken={
            ALL_TOKENS[buyToken] || {
              symbol: "AAVE",
              logoURI:
                "https://tokens.1inch.io/******************************************.png",
            }
          }
          fromAmount={sellAmountRef.current || "0"}
          toAmount={buyAmountRef.current || "0"}
          fromUsdValue={sellUsdValueRef.current || "0"}
          toUsdValue={buyUsdValueRef.current || "0"}
          swapData={swapDataRef.current}
          onConfirm={() => {
            console.log("Starting swap transaction...");
            // This will be connected to the actual swap function
          }}
          savedSlippageValue={0.5}
          savedAddedPriority={0}
          provider={provider}
          chainId={chain_id}
          pendingTxStatus={pendingTxStatus}
          transaction={null}
          inSwap={false}
          devMode={process.env.NODE_ENV === "development"}
        />
      )}
      {/* Development mode only - Transaction popup demo */}
      {/*       {process.env.NODE_ENV === "development" && (
        <div
          style={{
            position: "fixed",
            bottom: "20px",
            right: "20px",
            zIndex: "100",
          }}
        >
          <PendingTransactionDemo />
        </div>
      )} */}
    </div>
  );
};

export default Swap;
