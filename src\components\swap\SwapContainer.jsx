// Main SwapContainer - orchestrates everything
import React, { useEffect, useContext } from "react";
import { BlockchainContext } from "../../context/BlockchainContext";
import { useSwapLogic } from "./hooks/useSwapLogic";
import { useTransactionFlow } from "./hooks/useTransactionFlow";
import QuoteDisplay from "./QuoteDisplay";
import TransactionModal from "./TransactionModal";

// Import UI components
import SwitchCurrenciesButton from "../common/SwitchButton";
import { DownArrow } from "../common/SVGMAIN.js";
import WalletIcon from "../common/svgs/WalletIcon";
import DollarValue from "../common/DollarValue";
// Helper function for balance display
const balanceDisplayFixer = (balance) => {
  if (!balance || balance === "0" || balance === "Error" || isNaN(balance)) {
    return "0";
  } else {
    return parseFloat(balance).toFixed(4);
  }
};

const SwapContainer = ({
  sellToken,
  buyToken,
  ALL_TOKENS,
  chain_id,
  sellUsdValueRef,
  buyUsdValueRef,
  inputAmount,
  outputAmount,
  setOutputAmount,
  handleShowTokenList,
  sellTokenDisplayBalance,
  buyTokenDisplayBalance,
  swapTokens,
}) => {
  const { account, authToken } = useContext(BlockchainContext);

  // Custom hooks for business logic
  const {
    sellAmount,
    setSellAmount,
    buyAmount,
    swapData,
    loading,
    error,
    isApprovalNeeded,
    fetchQuote,
    inSwap,
  } = useSwapLogic(sellToken, buyToken, ALL_TOKENS, chain_id);

  const {
    showTransactionFlow,
    pendingTxStatus,
    currentStep,
    isProcessing,
    openTransactionFlow,
    closeTransactionFlow,
    executeTransaction,
    updateTransactionStatus,
    getTransactionData,
    getStepState,
    getButtonText,
    isButtonDisabled,
  } = useTransactionFlow();

  // Sync with global input amount
  useEffect(() => {
    const inputAmountNum = Number(inputAmount);
    console.log(
      "🔄 SwapContainer sync - inputAmount:",
      inputAmount,
      "sellAmount:",
      sellAmount
    );
    if (String(inputAmountNum) !== String(sellAmount)) {
      console.log("✅ Setting sellAmount to:", String(inputAmountNum));
      setSellAmount(String(inputAmountNum));
    }
  }, [inputAmount, sellAmount, setSellAmount]);

  // Fetch quote when relevant data changes
  useEffect(() => {
    console.log(
      "🔍 Quote check - sellAmount:",
      sellAmount,
      "sellToken:",
      sellToken,
      "buyToken:",
      buyToken,
      "account:",
      account
    );
    if (sellAmount > 0 && sellToken && buyToken && account) {
      console.log("✅ Calling fetchQuote");
      fetchQuote();
    } else {
      console.log("❌ Quote conditions not met");
    }
  }, [sellAmount, sellToken, buyToken, account, fetchQuote]);

  // Update output amount when buyAmount changes
  useEffect(() => {
    if (setOutputAmount && buyAmount !== outputAmount) {
      setOutputAmount(buyAmount);
    }
  }, [buyAmount, outputAmount, setOutputAmount]);

  // Handle transaction status updates
  useEffect(() => {
    if (pendingTxStatus === "confirmed" || pendingTxStatus === "failed") {
      updateTransactionStatus(pendingTxStatus);
    }
  }, [pendingTxStatus, updateTransactionStatus]);

  // Transaction handlers (these would be passed from parent or created here)
  const handleSwap = async () => {
    try {
      updateTransactionStatus("pending");
      // Actual swap logic would go here
      // For now, simulate success after delay
      setTimeout(() => {
        updateTransactionStatus("confirmed");
      }, 3000);
    } catch (error) {
      updateTransactionStatus("failed");
      throw error;
    }
  };

  const handleApprove = async () => {
    try {
      // Actual approval logic would go here
      // For now, simulate success
      console.log("Approval completed");
    } catch (error) {
      console.error("Approval failed:", error);
      throw error;
    }
  };

  // Button click handlers
  const handleApproveClick = () => {
    const transactionData = {
      sellAmount: sellAmount,
      buyAmount: buyAmount,
      sellUsdValue: sellUsdValueRef?.current || "0",
      buyUsdValue: buyUsdValueRef?.current || "0",
      swapData: swapData,
      isApprovalNeeded: true,
      handleSwap: handleSwap,
      handleApprove: handleApprove,
    };

    openTransactionFlow(transactionData);
  };

  const handleSwapClick = () => {
    const transactionData = {
      sellAmount: sellAmount,
      buyAmount: buyAmount,
      sellUsdValue: sellUsdValueRef?.current || "0",
      buyUsdValue: buyUsdValueRef?.current || "0",
      swapData: swapData,
      isApprovalNeeded: isApprovalNeeded,
      handleSwap: handleSwap,
      handleApprove: handleApprove,
    };

    openTransactionFlow(transactionData);
  };

  const currentTransactionData = getTransactionData();

  // Helper function to format amounts
  const formatAmount = (amount) => {
    try {
      if (!amount || amount === "0" || amount === "") {
        return "";
      }
      const parsedAmount = parseFloat(amount);
      if (isNaN(parsedAmount)) {
        return "";
      }
      return parsedAmount.toFixed(8);
    } catch (error) {
      return "";
    }
  };

  return (
    <>
      {/* Input/Output UI */}
      <div className="tokens-select-container">
        {/* YouPay Component */}
        <div className="flex-col">
          <div className="token-input-box">
            <div className="token-input-box-top">
              <div className="small-text">Sell</div>
              <div
                className="token-select-box"
                onClick={() => handleShowTokenList("sellToken")}
              >
                <img
                  src={ALL_TOKENS[sellToken]?.logo_uri}
                  alt={ALL_TOKENS[sellToken]?.name}
                  width={25}
                  height={25}
                  style={{ borderRadius: "50%" }}
                />
                <div className="med-text">{ALL_TOKENS[sellToken]?.symbol}</div>
                <DownArrow />
              </div>
            </div>
            <div className="flex-row bt">
              <input
                className="token-input"
                placeholder="0"
                type="number"
                value={inputAmount || ""}
                readOnly
              />
            </div>
            <div className="flex-row">
              <div className="small-text">
                <DollarValue
                  Token={ALL_TOKENS[sellToken]}
                  isOutputToken={false}
                  onValueChange={(value) => {
                    if (sellUsdValueRef) sellUsdValueRef.current = value;
                  }}
                />
              </div>
              <div className="small-text">
                <div className="max-row">
                  <WalletIcon />
                  {balanceDisplayFixer(sellTokenDisplayBalance)}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Swap Button */}
        <div className="swap-tokens" onClick={swapTokens}>
          <SwitchCurrenciesButton />
        </div>

        {/* YouReceive Component */}
        <div className="flex-col">
          <div className="token-input-box">
            <div className="token-input-box-top">
              <div className="small-text">Buy</div>
              <div
                className="token-select-box"
                onClick={() => handleShowTokenList("buyToken")}
              >
                <img
                  src={ALL_TOKENS[buyToken]?.logo_uri}
                  alt={ALL_TOKENS[buyToken]?.name}
                  width={25}
                  height={25}
                  style={{ borderRadius: "50%" }}
                />
                <div className="med-text">{ALL_TOKENS[buyToken]?.symbol}</div>
                <DownArrow />
              </div>
            </div>
            <div className="flex-row bt">
              <input
                className="token-input"
                placeholder="0"
                value={formatAmount(outputAmount)}
                type="number"
                readOnly
              />
            </div>
            <div className="flex-row">
              <div className="small-text">
                {outputAmount && Number(outputAmount) > 0 ? (
                  <DollarValue
                    Token={ALL_TOKENS[buyToken]}
                    isOutputToken={true}
                    onValueChange={(value) => {
                      if (buyUsdValueRef) buyUsdValueRef.current = value;
                    }}
                  />
                ) : (
                  "-"
                )}
              </div>
              <div className="small-text">
                <div className="max-row">
                  <WalletIcon />
                  {balanceDisplayFixer(buyTokenDisplayBalance)}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Quote Display / Swap Button */}
      <QuoteDisplay
        account={account}
        swapData={swapData}
        loading={loading}
        error={error}
        sellAmount={sellAmount}
        buyAmount={buyAmount}
        isApprovalNeeded={isApprovalNeeded}
        onApproveClick={handleApproveClick}
        onSwapClick={handleSwapClick}
      />

      {/* Transaction Modal */}
      {showTransactionFlow && (
        <TransactionModal
          isOpen={showTransactionFlow}
          onClose={closeTransactionFlow}
          fromToken={
            ALL_TOKENS[sellToken] || {
              symbol: "ETH",
              logoURI:
                "https://tokens.1inch.io/******************************************.png",
              logo_uri:
                "https://tokens.1inch.io/******************************************.png",
            }
          }
          toToken={
            ALL_TOKENS[buyToken] || {
              symbol: "AAVE",
              logoURI:
                "https://tokens.1inch.io/******************************************.png",
              logo_uri:
                "https://tokens.1inch.io/******************************************.png",
            }
          }
          fromAmount={currentTransactionData.sellAmount || "0"}
          toAmount={currentTransactionData.buyAmount || "0"}
          fromUsdValue={currentTransactionData.sellUsdValue || "0"}
          toUsdValue={currentTransactionData.buyUsdValue || "0"}
          isApprovalNeeded={currentTransactionData.isApprovalNeeded}
          currentStep={currentStep}
          getStepState={getStepState}
          onExecuteTransaction={executeTransaction}
          getButtonText={getButtonText}
          isButtonDisabled={isButtonDisabled}
          devMode={false}
        />
      )}
    </>
  );
};

export default SwapContainer;
