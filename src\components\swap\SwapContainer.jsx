// Main SwapContainer - orchestrates everything
import React, { useEffect, useContext } from 'react';
import { BlockchainContext } from '../../context/BlockchainContext';
import { useSwapLogic } from '../../hooks/useSwapLogic';
import { useTransactionFlow } from '../../hooks/useTransactionFlow';
import QuoteDisplay from './QuoteDisplay';
import TransactionModal from './TransactionModal';

const SwapContainer = ({ 
  sellToken, 
  buyToken, 
  ALL_TOKENS, 
  chain_id,
  sellUsdValueRef,
  buyUsdValueRef,
}) => {
  const { account, savedInputAmount, authToken } = useContext(BlockchainContext);

  // Custom hooks for business logic
  const {
    sellAmount,
    setSellAmount,
    buyAmount,
    swapData,
    loading,
    error,
    isApprovalNeeded,
    fetchQuote,
    inSwap,
  } = useSwapLogic(sellToken, buyToken, ALL_TOKENS, chain_id, authToken);

  const {
    showTransactionFlow,
    pendingTxStatus,
    currentStep,
    isProcessing,
    openTransactionFlow,
    closeTransactionFlow,
    executeTransaction,
    updateTransactionStatus,
    getTransactionData,
    getStepState,
    getButtonText,
    isButtonDisabled,
  } = useTransactionFlow();

  // Sync with global input amount
  useEffect(() => {
    const inputAmount = Number(savedInputAmount.current);
    if (String(inputAmount) !== String(sellAmount)) {
      setSellAmount(String(inputAmount));
    }
  }, [savedInputAmount.current, sellAmount, setSellAmount]);

  // Fetch quote when relevant data changes
  useEffect(() => {
    if (sellAmount > 0 && sellToken && buyToken && account) {
      fetchQuote();
    }
  }, [sellAmount, sellToken, buyToken, account, fetchQuote]);

  // Handle transaction status updates
  useEffect(() => {
    if (pendingTxStatus === 'confirmed' || pendingTxStatus === 'failed') {
      updateTransactionStatus(pendingTxStatus);
    }
  }, [pendingTxStatus, updateTransactionStatus]);

  // Transaction handlers (these would be passed from parent or created here)
  const handleSwap = async () => {
    try {
      updateTransactionStatus('pending');
      // Actual swap logic would go here
      // For now, simulate success after delay
      setTimeout(() => {
        updateTransactionStatus('confirmed');
      }, 3000);
    } catch (error) {
      updateTransactionStatus('failed');
      throw error;
    }
  };

  const handleApprove = async () => {
    try {
      // Actual approval logic would go here
      // For now, simulate success
      console.log('Approval completed');
    } catch (error) {
      console.error('Approval failed:', error);
      throw error;
    }
  };

  // Button click handlers
  const handleApproveClick = () => {
    const transactionData = {
      sellAmount: sellAmount,
      buyAmount: buyAmount,
      sellUsdValue: sellUsdValueRef?.current || "0",
      buyUsdValue: buyUsdValueRef?.current || "0",
      swapData: swapData,
      isApprovalNeeded: true,
      handleSwap: handleSwap,
      handleApprove: handleApprove,
    };
    
    openTransactionFlow(transactionData);
  };

  const handleSwapClick = () => {
    const transactionData = {
      sellAmount: sellAmount,
      buyAmount: buyAmount,
      sellUsdValue: sellUsdValueRef?.current || "0",
      buyUsdValue: buyUsdValueRef?.current || "0",
      swapData: swapData,
      isApprovalNeeded: isApprovalNeeded,
      handleSwap: handleSwap,
      handleApprove: handleApprove,
    };
    
    openTransactionFlow(transactionData);
  };

  const currentTransactionData = getTransactionData();

  return (
    <>
      {/* Quote Display */}
      <QuoteDisplay
        account={account}
        swapData={swapData}
        loading={loading}
        error={error}
        sellAmount={sellAmount}
        buyAmount={buyAmount}
        isApprovalNeeded={isApprovalNeeded}
        onApproveClick={handleApproveClick}
        onSwapClick={handleSwapClick}
      />

      {/* Transaction Modal */}
      {showTransactionFlow && (
        <TransactionModal
          isOpen={showTransactionFlow}
          onClose={closeTransactionFlow}
          fromToken={ALL_TOKENS[sellToken] || {
            symbol: "ETH",
            logoURI: "https://tokens.1inch.io/******************************************.png",
            logo_uri: "https://tokens.1inch.io/******************************************.png",
          }}
          toToken={ALL_TOKENS[buyToken] || {
            symbol: "AAVE",
            logoURI: "https://tokens.1inch.io/******************************************.png",
            logo_uri: "https://tokens.1inch.io/******************************************.png",
          }}
          fromAmount={currentTransactionData.sellAmount || "0"}
          toAmount={currentTransactionData.buyAmount || "0"}
          fromUsdValue={currentTransactionData.sellUsdValue || "0"}
          toUsdValue={currentTransactionData.buyUsdValue || "0"}
          isApprovalNeeded={currentTransactionData.isApprovalNeeded}
          currentStep={currentStep}
          getStepState={getStepState}
          onExecuteTransaction={executeTransaction}
          getButtonText={getButtonText}
          isButtonDisabled={isButtonDisabled}
          devMode={false}
        />
      )}
    </>
  );
};

export default SwapContainer;
