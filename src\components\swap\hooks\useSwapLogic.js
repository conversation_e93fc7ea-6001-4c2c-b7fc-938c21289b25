// Custom hook for swap business logic
import { useState, useRef, useCallback } from "react";

export const useSwapLogic = (sellToken, buyToken, ALL_TOKENS) => {
  // Core state
  const [sellAmount, setSellAmount] = useState(0);
  const [buyAmount, setBuyAmount] = useState("");
  const [swapData, setSwapData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isApprovalNeeded, setIsApprovalNeeded] = useState(false);

  // Refs for transaction state
  const inSwap = useRef(false);

  // Quote fetching logic - simplified for now
  const fetchQuote = useCallback(async () => {
    if (
      !ALL_TOKENS[sellToken] ||
      !ALL_TOKENS[buyToken] ||
      !sellAmount ||
      sellAmount === 0
    ) {
      setSwapData(null);
      setBuyAmount("0");
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const sellTokenData = ALL_TOKENS[sellToken];
      const buyTokenData = ALL_TOKENS[buyToken];

      // Simple placeholder calculation for testing
      const mockBuyAmount = (parseFloat(sellAmount) * 1.5).toFixed(6);
      console.log(
        "💰 Quote calculated - sellAmount:",
        sellAmount,
        "buyAmount:",
        mockBuyAmount
      );
      setBuyAmount(mockBuyAmount);

      // Create basic swap data
      const newSwapData = {
        sellToken: sellTokenData,
        buyToken: buyTokenData,
        sellAmount: sellAmount,
        buyAmount: mockBuyAmount,
      };

      setSwapData(newSwapData);

      // Check if approval is needed (simplified)
      if (sellTokenData.symbol === "ETH") {
        setIsApprovalNeeded(false);
      } else {
        setIsApprovalNeeded(true);
      }
    } catch (err) {
      console.error("Quote fetch failed:", err);
      setError(err.message);
      setSwapData(null);
      setBuyAmount("0");
    } finally {
      setLoading(false);
    }
  }, [sellAmount, sellToken, buyToken, ALL_TOKENS]);

  return {
    // State
    sellAmount,
    setSellAmount,
    buyAmount,
    setBuyAmount,
    swapData,
    loading,
    error,
    isApprovalNeeded,

    // Actions
    fetchQuote,

    // Refs
    inSwap,
  };
};
